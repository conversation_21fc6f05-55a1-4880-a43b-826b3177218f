import { DecodedJWT, IUserClaims, IUserRole } from 'SharedModules.Library';
import { HttpClient } from '@angular/common/http';
import { computed, EnvironmentInjector, inject, Injectable, runInInjectionContext, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { SocialAuthService } from "@abacritt/angularx-social-login";

const DECODED_JWT_DATA_KEY = "decodedJWTData";
const USER_KEY = "user";
const USER_TOKEN_KEY = "token";
const REFRESH_TOKEN_KEY = "refreshToken";
const HAS_SET_PASSWORD_ONE_TIME = "hasSetPasswordOneTime";

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  IUserRole: typeof IUserRole = IUserRole;
  private user?: IUserClaims;
  private tokenTimer: ReturnType<typeof setTimeout> | null = null;
  private userId?: string | null;
  public authStatusListener = new Subject<boolean>();
  public teacherStatus?: string;
  private _userId: string | null = null; // private property to store userId value
  private token!: string | null;
  refreshToken!: string;
  private envInjector = inject(EnvironmentInjector);

  public userAuthenticated = signal<boolean>(
    JSON.parse(localStorage.getItem(DECODED_JWT_DATA_KEY)!) !== null ? true : false
  );

  userAuthenticated$ = computed(() => {
    return this.userAuthenticated();
  });

  userData = signal({} as IUserClaims | undefined);
  userDecodedJWTData = signal(JSON.parse(localStorage.getItem(DECODED_JWT_DATA_KEY)!) as IUserClaims | undefined);

  userDecodedJWTData$ = computed(() => {
    return this.userDecodedJWTData() || null;
  });
  public disabledProfileRoutes = false;
  constructor(
    private http: HttpClient,
    private router: Router,
    // private _externalAuthService: SocialAuthService
  ) {
  }

  handleUserDataAndDecodeJWT(response: { token: string; refreshToken: string; userDto?: IUserClaims }) {
    this.setToken(response.token);
    this.setRefreshToken(response.refreshToken);
    this.setUser(response.userDto!);

    let token = response.token;
    let decodedJWT = window.atob(token.split('.')[1]);
    let decodedObject = JSON.parse(decodedJWT, (key, value) => {
      console.log(key, value);
      if (value === "True") {
        return true;
      } else if (value === "False") {
        return false;
      }
      // else if (key === "role") {
      //   return 'Student';
      // }
      else {
        return value;
      }
    });
    this.setUserDecodedData(decodedObject);
    localStorage.setItem(USER_KEY, JSON.stringify(this.userDecodedJWTData$()));
    this.userData.set(decodedObject);
  }

  handleUserDataAndDecodeJWTOnly(response: { token: string; refreshToken: string }) {
    this.setToken(response.token);
    this.setRefreshToken(response.refreshToken);

    let token = response.token;
    let decodedJWT = window.atob(token.split('.')[1]);
    let decodedObject = JSON.parse(decodedJWT, (key, value) => {
      if (value === "True") {
        return true;
      } else if (value === "False") {
        return false;
      } else {
        return value;
      }
    });
    this.setUserDecodedData(decodedObject);
    localStorage.setItem(USER_KEY, JSON.stringify(this.userDecodedJWTData$()));
    this.userData.set(decodedObject);
  }

  getToken() {
    return this.token;
  }

  getUserId() {
    return this.userId || this._userId; // return userId from private property if it exists, otherwise from local storage
  }

  setUserId(userId: string) {
    this._userId = userId; // set userId value in private property
  }

  getLoggedInUser() {
    const authInformation = this.getAuthData();
    if (authInformation) {
      return JSON.parse(authInformation?.user!);
    }
    return {}
  }

  getAuthStatusListener() {
    return this.authStatusListener.asObservable();
  }

  setToken(tokenValue: string) {
    localStorage.setItem(USER_TOKEN_KEY, tokenValue);
    this.token = tokenValue;
  }

  setRefreshToken(tokenValue: string) {
    localStorage.setItem(REFRESH_TOKEN_KEY, tokenValue);
    this.refreshToken = tokenValue;
  }

  setUser(user: IUserClaims) {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
    this.userData.set(user);
  }

  setUserDecodedData(decodedJWTData: IUserClaims) {
    localStorage.setItem(DECODED_JWT_DATA_KEY, JSON.stringify(decodedJWTData));
    this.userDecodedJWTData.set(decodedJWTData);
  }

  setHasSetPasswordOneTime(tokenValue: { setPasswordOneTime: boolean }) {
    localStorage.setItem(HAS_SET_PASSWORD_ONE_TIME, JSON.stringify(tokenValue));
  }

  setUserData(user: IUserClaims) {
    this.userData.set(user);
  }

  setAuthTimer(duration: number) {
    this.tokenTimer = setTimeout(() => {
      this.logout();
    }, duration * 1000)
  }

  setDisabledProfileRoutes(value: boolean) {
    this.disabledProfileRoutes = value;
  }

  getUserDecodedData(): DecodedJWT | null {
    const storedData = localStorage.getItem(DECODED_JWT_DATA_KEY);
    if (storedData) {
      return JSON.parse(storedData);
    }
    return null;
  }

  getUserRole(): IUserRole {
    let storedData = localStorage.getItem(DECODED_JWT_DATA_KEY);
    if (storedData) {
      return JSON.parse(storedData)?.role as IUserRole;
    } else {
      return this.userDecodedJWTData$()?.role as IUserRole || null;
    }
  }

  getRefreshToken() {
    const storedData = localStorage.getItem(REFRESH_TOKEN_KEY);
    console.log(storedData);
    if (storedData) {
      return (storedData);
    }
    return this.refreshToken;
  }

  getHasSetPasswordOneTime(): { setPasswordOneTime: boolean } | null {
    const storedData = localStorage.getItem(HAS_SET_PASSWORD_ONE_TIME);
    console.log(storedData);
    if (storedData) {
      return JSON.parse(storedData);
    }
    return null;
  }

  removeHasSetPasswordOneTime() {
    localStorage.removeItem(HAS_SET_PASSWORD_ONE_TIME);
  }

  isAuth(): boolean {
    const token = localStorage.getItem(USER_TOKEN_KEY);
    const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);

    // Check if token and expiration exist
    if (!token || !refreshToken) {
      return false; // No token or expiration, not authenticated
    }
    return true; // Token valid, user is authenticated
  }

  getAuthData() {
    const token = localStorage.getItem(USER_TOKEN_KEY);
    const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
    const userId = localStorage.getItem("userId");
    const user = localStorage.getItem(USER_KEY);
    const role = this.getUserDecodedData()?.role;
    return {
      token: token as string,
      refreshToken: refreshToken as string,
      userId: userId,
      user: user,
      role: role
    }
  }


  clearAuthTimer() {
    if (this.tokenTimer !== null) { // Check if tokenTimer is not null
      clearTimeout(this.tokenTimer);
      this.tokenTimer = null; // Reset the timer
    }
  }

  logout() {
    // const userRole = this.getUserRole();
    this.token = null;
    this.authStatusListener.next(false);
    this.userId = null;
    this.user = {} as IUserClaims;
    this.clearAuthTimer();
    this.logoutRoute(null);
    this.clearAuthData();
    this.userAuthenticated.set(false);
    this.userData.set(undefined);
    sessionStorage.clear();
    runInInjectionContext(this.envInjector, () => {
      const socialAuthService = inject(SocialAuthService);
      socialAuthService.signOut();
    });
  }

  goTologinRoute() {
    let routePath: string;
    console.log(this.getUserRole());
    routePath = "/auth/login";
    this.router.navigate([routePath]);
    return;
  }

  goToRegisterRoute() {
    let routePath: string;
    console.log(this.getUserRole());
    routePath = "/auth/register";
    this.router.navigate([routePath]);
    return;
  }

  logoutRoute(userRole: IUserRole | null) {
    let routePath: string;
    console.log(this.getUserRole());
    routePath = "/auth/login";
    this.router.navigate([routePath]);
    return;
    switch (userRole) {
      case IUserRole.PARENT:
        routePath = "/auth/parent";
        break;
      case IUserRole.TEACHER:
        routePath = "/auth/teacher";
        break;
      case IUserRole.STUDENT:
        routePath = "/auth/student";
        break;
      default:
        routePath = "/auth/parent";
        break;
    }

    this.router.navigate([routePath]);
  }

  getRouteByRole(): string | null {
    const userRole = this.getUserRole();
    switch (userRole) {
      case IUserRole.PARENT:
        return '/dashboard/parent/overview';
      case IUserRole.TEACHER:
        return '/dashboard/teacher/overview';
      case IUserRole.STUDENT:
        return '/dashboard/student/overview';
      case IUserRole.ADMIN:
        return '/'; // Redirect to the admin dashboard or home
      default:
        return null; // Return null if the role is not recognized
    }
  }

  goToDashboard() {
    const userRole = this.getUserRole();
    switch (userRole) {
      case IUserRole.PARENT:
        this.router.navigate(["/dashboard/parent/overview"]);
        break;
      case IUserRole.TEACHER:
        this.router.navigate(["/dashboard/teacher/overview"]);
        break;
      case IUserRole.STUDENT:
        this.router.navigate(["/dashboard/student/overview"]);
        break;
      default:
        break;
    }
  }

  dashboardHomeRoutePerRole(): string | null {
    const userRole = this.getUserRole();
    switch (userRole) {
      case IUserRole.PARENT:
        return '/dashboard/parent/overview';
      case IUserRole.TEACHER:
        return '/dashboard/teacher/overview';
      case IUserRole.STUDENT:
        return '/dashboard/student/overview';
      default:
        return null; // Return null or a default route if the role is unknown
    }
  }

  goToDashboardPerRole() {
    const userRole = this.getUserRole();
    switch (userRole) {
      case IUserRole.PARENT:
        this.router.navigate(["/dashboard/parent/overview"]);
        break;
      case IUserRole.TEACHER:
        this.router.navigate(["/dashboard/teacher/overview"]);
        break;
      case IUserRole.STUDENT:
        this.router.navigate(["/dashboard/student/overview"]);
        break;
      default:
        break;
    }
  }

  goToNotificationsPerRole() {
    const userRole = this.getUserRole();
    switch (userRole) {
      case IUserRole.PARENT:
        this.router.navigate(["/dashboard/parent/notifications"]);
        break;
      case IUserRole.TEACHER:
        this.router.navigate(["/dashboard/teacher/notifications"]);
        break;
      case IUserRole.STUDENT:
        this.router.navigate(["/dashboard/student/notifications"]);
        break;
      default:
        break;
    }
  }

  goToUserSettings() {
    this.router.navigate(["/user-settings/account-info"]);
  }


  private clearAuthData() {
    localStorage.removeItem(USER_KEY);
    localStorage.removeItem(USER_TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(DECODED_JWT_DATA_KEY);
    localStorage.removeItem(HAS_SET_PASSWORD_ONE_TIME);
    this.userDecodedJWTData.set(undefined);
    this.userData.set(undefined);
    this.teacherStatus = undefined;
    this._userId = null;
  }

  public getUser() {
    const user = localStorage.getItem(USER_KEY);
    if (user) {
      return JSON.parse(user);
    }
    return this.userData();
  }

}
