// ... existing code ...
import { ChangeDetectionStrategy, Component, computed, DestroyRef, EventEmitter, inject, Injector, input, linkedSignal, model, OnDestroy, OnInit, Output, signal, Signal } from '@angular/core';
import { CardSplitLayoutComponent } from '@platform.app/shared/components/card-split-layout/card-split-layout.component';
import { StudentGroupSelectionSuggestionTextStepComponent } from '@platform.app/shared/components/dialogs/student-group-selection-dialog/student-group-selection-suggestion-text-step/student-group-selection-suggestion-text-step.component';
import { ButtonModule } from 'primeng/button';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ApiLoadingStateService, AuthStateService, AvailabilityPickerDaysComponent, AvailabilityTimezoneSelectorComponent, DataApiStateService, DefaultGetStudentsRequest, EmitEvent, EventBusService, Events, FormErrorScrollerService, FormFieldValidationMessageComponent, GeneralService, GenericPrimeDropdownOption, GenericPrimeEnumToDropdownOptionsConfig, GroupDialogState, HandleApiResponseService, IApiResponseBase, IAvailability, IAvailabilityDto, IBasicProfileInfoDto, ICreateAvailabilityDto, ICreateStudentGroupRequest, ICreateStudentGroupResponse, IEditStudentGroupRequest, IFindCommonTimeSlotsResponse, IGetAllTeachingLanguagesResponse, IGetAvailabilityResponse, IGetStudentGroupResponse, IGetStudentsResponse, IGetTeachingLanguageResponse, ILanguageLevelsEnum, ISearchStudentDto, IStudentGroupDto, IStudentLevelEnum, ITeachingLanguageDto, ITimeZoneIdData, IWeekDayTimeSlotDto, PrimeDropdownComponent, PrimeReactiveFormInputComponent, StudentGroupRoutes, ToastService } from 'SharedModules.Library';
import { Router } from '@angular/router';
import { CarouselService } from '@platform.app/core/services/carousel.service';
import { StudentGroupService } from '@platform.app/core/services/student-group.service';
import { UserService } from '@platform.app/core/services/user.service';
import { PrimeStudentsSelectionComponent } from '@platform.app/shared/components/prime/prime-students-selection/prime-students-selection.component';
import { PrimeStudentGroupSelectionComponent } from '@platform.app/shared/components/prime/prime-student-group-selection/prime-student-group-selection.component';
import { InputTextModule } from 'primeng/inputtext';
import { SelectChangeEvent, SelectModule } from 'primeng/select';
import { toObservable } from '@angular/core/rxjs-interop';
import { TextareaModule } from 'primeng/textarea';
import { ActivatedRoute } from '@angular/router';
import { SmartStudentSelectorComponent } from '../smart-student-selector/smart-student-selector.component';

// Type helper to create FormControl types from interface
type FormControlsOf<T> = {
  [K in keyof T]: K extends 'availability'
  ? FormControl<T[K] | null>
  : T[K] extends object
  ? T[K] extends any[]
  ? FormControl<T[K] | null>
  : FormGroup<FormControlsOf<T[K]>>
  : FormControl<T[K] | null>;
};

@Component({
  selector: 'app-parent-overview-group-create-form',
  imports: [
    CommonModule,
    CardSplitLayoutComponent,
    ButtonModule,
    StudentGroupSelectionSuggestionTextStepComponent,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    TextareaModule,
    SelectModule,
    AvailabilityPickerDaysComponent,
    AvailabilityTimezoneSelectorComponent,
    PrimeStudentsSelectionComponent,
    PrimeStudentGroupSelectionComponent,
    PrimeReactiveFormInputComponent,
    PrimeDropdownComponent,
    FormFieldValidationMessageComponent,
    SmartStudentSelectorComponent,
  ],
  templateUrl: './parent-overview-group-create-form.component.html',
  styleUrl: './parent-overview-group-create-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentOverviewGroupCreateFormComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  groupForm!: FormGroup<FormControlsOf<ICreateStudentGroupRequest>>;
  editGroupForm: FormGroup<FormControlsOf<IEditStudentGroupRequest>> = new FormGroup<FormControlsOf<IEditStudentGroupRequest>>({} as any);

  // Injected services
  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    user: inject(UserService),
    api: inject(HandleApiResponseService),
    studentGroup: inject(StudentGroupService),
    dataState: inject(DataApiStateService),
    toast: inject(ToastService),
    eventBus: inject(EventBusService),
    carousel: inject(CarouselService),
    formErrorScroller: inject(FormErrorScrollerService),
    apiLoadingStateService: inject(ApiLoadingStateService),
    router: inject(Router)
  };

  private readonly injector = inject(Injector);
  readonly EditGroupStateEnum = GroupDialogState;
  protected readonly GroupDialogState = GroupDialogState;
  isDialogPopup = model<boolean>(false);
  isEditMode = model<boolean>(false);
  showTopTitle = model<boolean>(true);
  groupId = model<string | null>(null);
  editGroupState = model<GroupDialogState>(GroupDialogState.CreateGroupSuggestionStep);
  @Output() onAvailabilityStepValidityChanged = new EventEmitter<boolean>();
  @Output() onGroupStateChanged = new EventEmitter<GroupDialogState>(undefined);
  @Output() onGroupCreated = new EventEmitter<ICreateStudentGroupResponse>();
  @Output() onGroupFormValidityChanged = new EventEmitter<boolean>();

  availabilityForm: FormGroup = new FormGroup({});
  availabilityData = signal<IWeekDayTimeSlotDto[]>([]);
  availabilityTimezone: ITimeZoneIdData = {} as ITimeZoneIdData;
  availabilityDataLoaded = signal(false);
  areAllCommonTimeSlotsEmptyBool = signal(true);
  isPanelClosed = false;
  selectedLanguageOfInterest = signal<string>('');
  selectedStudents = signal<ISearchStudentDto[]>([]);
  allStudents$ = signal<ISearchStudentDto[]>([]);
  // Raw students data from API
  rawStudents$: Signal<ISearchStudentDto[]> = computed(() => {
    const studentState = this.services.dataState.parentStudents.state();
    const studentsResponse: IGetStudentsResponse = studentState.data as IGetStudentsResponse;
    return studentsResponse?.pageData ?? [];
  });

  // Filtered students based on selected language and availability
  students$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    // If no language is selected, return all students
    if (!selectedLanguageId) {
      return allStudents;
    }

    // Filter students by selected language ID and where groupId is null (not in any group)
    const filteredStudents = allStudents.filter(student => {
      if (!student.studentTeachingLanguageDto || !Array.isArray(student.studentTeachingLanguageDto)) {
        return false;
      }

      return student.studentTeachingLanguageDto.some((lang: any) =>
        lang.teachingLanguageId === selectedLanguageId &&
        (lang.groupId === null || lang.groupId === undefined)
      );
    });

    // Log filtering results for debugging
    console.log('🎯 Language filter applied:', {
      selectedLanguageId,
      totalStudents: allStudents.length,
      filteredStudents: filteredStudents.length,
      filterCriteria: 'teachingLanguageId matches AND groupId is null'
    });

    return filteredStudents;
  });

  teachingLanguages$ = computed(() => this.services.dataState.teachingLanguages.state().data as IGetAllTeachingLanguagesResponse);

  // Available students for group creation (not already in a group for the selected language)
  availableStudentsForLanguage$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    if (!selectedLanguageId) {
      return [];
    }

    return allStudents.filter(student => {
      if (!student.studentTeachingLanguageDto || !Array.isArray(student.studentTeachingLanguageDto)) {
        return false;
      }

      // Find the specific language entry for the selected language
      const languageEntry = student.studentTeachingLanguageDto.find((lang: any) =>
        lang.teachingLanguageId === selectedLanguageId
      );

      // Student is available if:
      // 1. They have the selected language
      // 2. They are not currently in a group for this language (groupId is null/undefined)
      if (this.isEditMode()) {
        // In edit mode, students in the current group are also available
        return languageEntry;
      } else {
        // In create mode, students are available if they are not in any group for this language
        return languageEntry && (languageEntry.groupId === null || languageEntry.groupId === undefined);
      }
    });
  });

  // Students already in groups for the selected language (for reference/debugging)
  studentsInGroupsForLanguage$: Signal<ISearchStudentDto[]> = computed(() => {
    const allStudents = this.rawStudents$();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    if (!selectedLanguageId) {
      return [];
    }

    return allStudents.filter(student => {
      if (!student.studentTeachingLanguageDto || !Array.isArray(student.studentTeachingLanguageDto)) {
        return false;
      }

      return student.studentTeachingLanguageDto.some((lang: any) =>
        lang.teachingLanguageId === selectedLanguageId &&
        lang.groupId !== null &&
        lang.groupId !== undefined
      );
    });
  });

  // Validation state
  hasMinimumStudents = computed(() => this.selectedStudents().length >= 2);

  // Debug method to check form validation status
  getFormValidationErrors(): string[] {
    const errors: string[] = [];
    if (!this.groupForm) return ['Form not initialized'];

    Object.keys(this.groupForm.controls).forEach(key => {
      const control = this.groupForm.get(key);
      if (control && control.invalid) {
        if (control.errors?.['required']) {
          errors.push(`${key} is required`);
        }
        if (control.errors) {
          errors.push(`${key}: ${JSON.stringify(control.errors)}`);
        }
      }
    });

    // Check nested availabilityDto
    const availabilityDto = this.groupForm.get('availabilityDto');
    if (availabilityDto && availabilityDto.invalid) {
      Object.keys((availabilityDto as FormGroup).controls).forEach(nestedKey => {
        const nestedControl = availabilityDto.get(nestedKey);
        if (nestedControl && nestedControl.invalid) {
          if (nestedControl.errors?.['required']) {
            errors.push(`availabilityDto.${nestedKey} is required`);
          }
          if (nestedControl.errors) {
            errors.push(`availabilityDto.${nestedKey}: ${JSON.stringify(nestedControl.errors)}`);
          }
        }
      });
    }

    return errors;
  }

  // Student level dropdown options
  studentLevels: GenericPrimeDropdownOption[] = [];

  // Computed group level from selected students' ILanguageLevelsEnum
  computedGroupLanguageLevels = computed(() => {
    const students = this.selectedStudents();
    const selectedLanguageId = this.selectedLanguageOfInterest();

    if (students.length === 0 || !selectedLanguageId) return [];

    const languageLevels = new Set<ILanguageLevelsEnum>();

    students.forEach(student => {
      if (student.studentTeachingLanguageDto) {
        student.studentTeachingLanguageDto.forEach(lang => {
          if (lang.teachingLanguageId === selectedLanguageId) {
            languageLevels.add(lang.languageLevel);
          }
        });
      }
    });

    return Array.from(languageLevels).sort();
  });

  // Display text for computed group levels
  computedGroupLevelsText = computed(() => {
    const levels = this.computedGroupLanguageLevels();
    if (levels.length === 0) return 'N/A';

    return levels.map(level => this.getLanguageLevelDisplayName(level)).join(', ');
  });

  // Suggested student level based on language levels
  suggestedStudentLevel = computed((): IStudentLevelEnum => {
    const levels = this.computedGroupLanguageLevels();
    if (levels.length === 0) return IStudentLevelEnum.Beginner;

    // Map ILanguageLevelsEnum to IStudentLevelEnum
    if (levels.some(level => [ILanguageLevelsEnum.A1, ILanguageLevelsEnum.A2].includes(level))) {
      return IStudentLevelEnum.Beginner;
    } else if (levels.some(level => [ILanguageLevelsEnum.B1, ILanguageLevelsEnum.B2].includes(level))) {
      return IStudentLevelEnum.Intermediate;
    } else if (levels.some(level => [ILanguageLevelsEnum.C1, ILanguageLevelsEnum.C2].includes(level))) {
      return IStudentLevelEnum.Advanced;
    }

    return IStudentLevelEnum.Beginner;
  });

  // Computed level from selected students - maps to IStudentLevelEnum
  computedLevel = computed((): IStudentLevelEnum => {
    const students = this.selectedStudents();
    if (students.length === 0) return IStudentLevelEnum.Beginner;

    // Extract all language levels from selected students
    const allLevels = new Set<ILanguageLevelsEnum>();
    students.forEach(student => {
      if (student.studentTeachingLanguageDto) {
        student.studentTeachingLanguageDto.forEach(lang => {
          allLevels.add(lang.languageLevel);
        });
      }
    });

    // Map ILanguageLevelsEnum to IStudentLevelEnum
    // This is a simplified mapping - you might want more sophisticated logic
    const levels = Array.from(allLevels);
    if (levels.includes(ILanguageLevelsEnum.A1) || levels.includes(ILanguageLevelsEnum.A2)) {
      return IStudentLevelEnum.Beginner;
    } else if (levels.includes(ILanguageLevelsEnum.B1) || levels.includes(ILanguageLevelsEnum.B2)) {
      return IStudentLevelEnum.Intermediate;
    } else if (levels.includes(ILanguageLevelsEnum.C1) || levels.includes(ILanguageLevelsEnum.C2)) {
      return IStudentLevelEnum.Advanced;
    }

    return IStudentLevelEnum.Beginner; // Default
  });

  // Form Options
  readonly levels = [
    { label: 'Select a Level *', value: null },
    { label: 'No Experience', value: IStudentLevelEnum.NoExperience },
    { label: 'Beginner', value: IStudentLevelEnum.Beginner },
    { label: 'Intermediate', value: IStudentLevelEnum.Intermediate },
    { label: 'Advanced', value: IStudentLevelEnum.Advanced }
  ];

  studentGroupItem = signal<any>({});
  originalStudents = signal<ISearchStudentDto[]>([]); // Track original students in the group for edit mode
  title = computed(() => this.isEditMode() ? 'Edit Student Group' : 'Create New Student Group');
  paragraphText = computed(() => this.isEditMode()
    ? 'Update your student group details below'
    : 'Create a new student group by selecting students and setting availability');

  constructor(private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.initializeStudentLevels();
    this.initEvents();

    if (this.isEditMode()) {
      this.editGroupForm = this.editGroupClassTypedFormGroup();
    } else {
      this.groupForm = this.createGroupClassTypedFormGroup();
    }

    if (this.isEditMode() && this.groupId()) {
      this.editGroupState.set(GroupDialogState.EditGroup);
      this.loadGroupDetails(this.groupId()!);
    } else {
      this.handleQueryParams();
    }


    // Watch for students data changes using toObservable
    toObservable(this.students$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((students: ISearchStudentDto[]) => {
        this.allStudents$.set(students);
      });

    // Watch for student selection changes to reset availability data
    toObservable(this.hasMinimumStudents, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((hasMinimum: boolean) => {
        if (!hasMinimum) {
          this.availabilityDataLoaded.set(false);
          this.areAllCommonTimeSlotsEmptyBool.set(true);
        }
      });

    if (!this.isEditMode()) {
      this.groupForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
        this.onGroupFormValidityChanged.emit(this.activeForm.valid);
      });
    }

    // Watch for form validity changes

  }

  ngOnDestroy(): void {
    // HTTP calls, router subscriptions, and form subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  private createGroupClassTypedFormGroup(): FormGroup<FormControlsOf<ICreateStudentGroupRequest>> {
    return new FormGroup<FormControlsOf<ICreateStudentGroupRequest>>({
      parentId: new FormControl<string | null>(this.services.auth.getUserClaims().id, Validators.required),
      teachingLanguageId: new FormControl<string | null>(null, Validators.required),
      studentsToAdd: new FormControl<string[] | null>(null, Validators.required),
      studentLevel: new FormControl<IStudentLevelEnum | null>(null, Validators.required),
      moreDetails: new FormControl<string | null>(null),
      groupName: new FormControl<string | null>(null),
      availabilityDto: new FormGroup<FormControlsOf<ICreateAvailabilityDto>>({
        weekDayTimeSlots: new FormControl<IWeekDayTimeSlotDto[] | null>(null, Validators.required)
      })
    });
  }

  private editGroupClassTypedFormGroup(): FormGroup<FormControlsOf<IEditStudentGroupRequest>> {
    return new FormGroup<FormControlsOf<IEditStudentGroupRequest>>({
      parentId: new FormControl<string | null>(this.services.auth.getUserClaims().id, Validators.required),
      groupId: new FormControl<string | null>(this.groupId(), Validators.required),
      moreDetails: new FormControl<string | null>(null),
      groupName: new FormControl<string | null>(null),
      studentsToAdd: new FormControl<string[] | null>(null),
      studentsToRemove: new FormControl<string[] | null>(null),
      studentLevel: new FormControl<IStudentLevelEnum | null>(null, Validators.required),
      availability: new FormControl<IAvailabilityDto | null>(null, Validators.required)
    });
  }

  // Get the active form based on mode
  get activeForm(): FormGroup {
    return this.isEditMode() ? this.editGroupForm : this.groupForm;
  }

  canCreateGroup(): boolean {
    return this.hasMinimumStudents() && this.activeForm.valid;
  }

  // Helper methods to access form controls
  getFormControl(name: string): AbstractControl | null {
    return this.activeForm.get(name);
  }

  onSubmit(): void {
    if (this.isEditMode()) {
      if (this.editGroupForm.valid) {
        this.updateStudentGroup();
      } else {
        this.editGroupForm.markAllAsTouched();
        this.services.formErrorScroller.validateAndScrollToFirstError(this.editGroupForm);
      }
    } else {
      if (this.groupForm.valid) {
        this.createStudentGroupRequest();
      } else {
        this.groupForm.markAllAsTouched();
        this.services.formErrorScroller.validateAndScrollToFirstError(this.groupForm);
      }
    }
  }

  private updateStudentGroup(): void {
    const formValue = this.editGroupForm.getRawValue();

    console.log('🚀 ~ ParentOverviewGroupsCreateComponent ~ updateStudentGroup ~ formValue:', formValue);
    // return;
    const request: IEditStudentGroupRequest = {
      parentId: this.services.auth.getUserClaims().id,
      groupId: this.groupId()!,
      groupName: formValue.groupName!,
      studentLevel: formValue.studentLevel!,
      moreDetails: formValue.moreDetails!,
      studentsToAdd: formValue.studentsToAdd!,
      studentsToRemove: formValue.studentsToRemove!,
      availability: formValue.availability!,
    }

    this.services.api.getApiData<IEditStudentGroupRequest>(
      { url: StudentGroupRoutes.patchEditStudentGroup, method: 'PATCH' },
      request
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: this.handleEditGroupSuccess,
    });
  }

  private handleEditGroupSuccess = (): void => {
    this.services.eventBus.emit(new EmitEvent(Events.StudentGroupEdited, undefined));
    this.services.toast.show({ severity: 'success', summary: 'Success', detail: 'Student group updated successfully' });
    this.services.router.navigate(['/dashboard/parent/groups/list']);
  }

  onLanguageChange(event: SelectChangeEvent): void {
    console.log('Language changed:', event.value);
    this.selectedLanguageOfInterest.set(event.value as string);
    this.availabilityDataLoaded.set(false);
    this.selectedStudents.set([]);
    this.loadStudents();

    // Debug: Log filtering results after language change
    this.logStudentFilteringResults();
  }

  /**
   * Get students filtered by language ID and group status
   * @param languageId - The teaching language ID to filter by
   * @param inGroup - true for students in groups, false for students not in groups
   * @returns Array of filtered students
   */
  getStudentsByLanguageAndGroupStatus(languageId: string, inGroup: boolean): ISearchStudentDto[] {
    const allStudents = this.rawStudents$();

    return allStudents.filter(student => {
      if (!student.studentTeachingLanguageDto || !Array.isArray(student.studentTeachingLanguageDto)) {
        return false;
      }

      const languageEntry = student.studentTeachingLanguageDto.find((lang: any) =>
        lang.teachingLanguageId === languageId
      );

      if (!languageEntry) return false;

      const hasGroupId = languageEntry.groupId !== null && languageEntry.groupId !== undefined;
      return inGroup ? hasGroupId : !hasGroupId;
    });
  }

  /**
   * Get available students for a specific language (not in any group)
   * @param languageId - The teaching language ID
   * @returns Array of available students
   */
  getAvailableStudentsForLanguage(languageId: string): ISearchStudentDto[] {
    return this.getStudentsByLanguageAndGroupStatus(languageId, false);
  }

  /**
   * Get students already in groups for a specific language
   * @param languageId - The teaching language ID
   * @returns Array of students in groups
   */
  getStudentsInGroupsForLanguage(languageId: string): ISearchStudentDto[] {
    return this.getStudentsByLanguageAndGroupStatus(languageId, true);
  }

  /**
   * Debug method to log student filtering results
   */
  private logStudentFilteringResults(): void {
    const selectedLanguageId = this.selectedLanguageOfInterest();
    if (!selectedLanguageId) return;

    const allStudents = this.rawStudents$();
    const availableStudents = this.availableStudentsForLanguage$();
    const studentsInGroups = this.studentsInGroupsForLanguage$();

    console.log('📊 Student Filtering Results:', {
      selectedLanguageId,
      totalStudents: allStudents.length,
      availableStudents: availableStudents.length,
      studentsInGroups: studentsInGroups.length,
      availableStudentNames: availableStudents.map(s => `${s.firstName} ${s.lastName}`),
      studentsInGroupNames: studentsInGroups.map(s => `${s.firstName} ${s.lastName}`)
    });

    // Log detailed language data for first few students for debugging
    allStudents.slice(0, 3).forEach(student => {
      const languageData = student.studentTeachingLanguageDto?.filter((lang: any) =>
        lang.teachingLanguageId === selectedLanguageId
      );

      console.log(`👤 ${student.firstName} ${student.lastName}:`, {
        languageData,
        hasSelectedLanguage: !!languageData?.length,
        groupIds: languageData?.map((lang: any) => lang.groupId)
      });
    });
  }

  onStudentSelected(students: ISearchStudentDto[]): void {
    console.log('Students selected:', students);
    this.selectedStudents.set(students);

    // Update form with selected student IDs
    if (this.isEditMode()) {
      // In edit mode, calculate studentsToAdd and studentsToRemove based on original students
      const originalStudentIds = this.originalStudents().map(s => s.userId);
      const currentStudentIds = students.map(s => s.userId);

      // Students to add: currently selected but not in original group
      const studentsToAdd = currentStudentIds.filter(id => !originalStudentIds.includes(id));

      // Students to remove: were in original group but not currently selected
      const studentsToRemove = originalStudentIds.filter(id => !currentStudentIds.includes(id));

      console.log('Edit mode student changes:', {
        originalStudentIds,
        currentStudentIds,
        studentsToAdd,
        studentsToRemove
      });

      this.editGroupForm.patchValue({
        studentsToAdd,
        studentsToRemove
      });
      console.log('Edit group form value:', this.editGroupForm.value);
    } else {
      // Create mode: simple assignment
      this.groupForm.patchValue({
        studentsToAdd: students.map(s => s.userId)
      });
    }

    // Fetch common time slots if multiple students selected and minimum requirement met
    if (students.length >= 2) {
      this.availabilityDataLoaded.set(false);
      this.fetchCommonTimeSlots(students.map(s => s.userId));
    } else {
      // In edit mode, preserve availability if we have existing students
      if (this.isEditMode() && this.originalStudents().length > 0) {
        // Keep existing availability data loaded
        this.availabilityDataLoaded.set(true);
      } else {
        // Reset availability data if below minimum
        this.availabilityDataLoaded.set(false);
        this.areAllCommonTimeSlotsEmptyBool.set(true);
      }
    }
  }

  // API Methods
  fetchCommonTimeSlots(studentIds: string[]): void {
    this.services.api.getApiData<IFindCommonTimeSlotsResponse>(
      { url: IAvailability.postFindCommonTimeSlots, method: 'POST' },
      { studentIds }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: IFindCommonTimeSlotsResponse) => this.handleFetchSuccess(response),
      error: (error: IApiResponseBase) => this.handleFetchError(error),
    });
  }

  private handleFetchError(error: IApiResponseBase): void {
    console.error('Error fetching common time slots:', error);
    this.availabilityDataLoaded.set(true);
  }

  private getGroupAvailability(availabilityId: string): void {
    this.services.api.getApiData<IGetAvailabilityResponse>(
      { url: IAvailability.getAvailability, method: 'GET' },
      { availabilityId }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: IGetAvailabilityResponse) => this.handleGetAvailabilitySuccess(response),
      error: () => this.availabilityDataLoaded.set(true)
    });
  }

  // Event Handlers
  onTimeSlotsChange(timeSlots: IWeekDayTimeSlotDto[]): void {
    if (this.isEditMode()) {
      this.editGroupForm.patchValue({
        availability: {
          timeZoneDisplayName: this.availabilityTimezone.timeZoneDisplayName,
          timeZoneIana: this.availabilityTimezone.timeZoneIana,
          timeZoneId: this.availabilityTimezone.timeZoneId!,
          weekDayTimeSlots: timeSlots,
          availabilityId: this.editGroupForm.get('availability')?.value?.availabilityId || undefined,
        }
      });
    } else {
      this.groupForm.patchValue({
        availabilityDto: {
          weekDayTimeSlots: timeSlots,
        }
      });
    }

    console.log(this.services.studentGroup.getEditStudentGroupRequest());
  }

  onTimeZoneChanged(timezone: ITimeZoneIdData): void {
    console.log(timezone);
    this.services.studentGroup.updateEditStudentGroupRequest({
      availability: {
        ...this.services.studentGroup.getEditStudentGroupRequest().availability,
        timeZoneDisplayName: timezone.timeZoneDisplayName,
        timeZoneIana: timezone.timeZoneIana,
      }
    });
  }

  onAvailabilityDaysFormValid(valid: boolean): void {
    this.onAvailabilityStepValidityChanged.emit(valid);
  }

  private createStudentGroupRequest() {
    let request: ICreateStudentGroupRequest  = this.groupForm.getRawValue() as ICreateStudentGroupRequest;

    console.log('Create group request:', request);
    // return;
    this.services.api.getApiData<ICreateStudentGroupResponse>(
      { url: StudentGroupRoutes.postCreateStudentGroup, method: 'POST' },
      request, false
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response: ICreateStudentGroupResponse) => {
        console.log('Group created', response);
        this.services.toast.show({ severity: 'success', summary: 'Success', detail: 'Student group created successfully' });

        this.onGroupCreated.emit(response);
        // this.services.router.navigate(['/dashboard/parent/groups']);
      }
    });
  }



  // Helper Methods
  private areAllCommonTimeSlotsEmpty(data: IWeekDayTimeSlotDto[]): boolean {
    return data.every(item => item.timeSlots.length === 0);
  }

  getSelectedLanguageName(): string {
    const selectedLanguageId = this.selectedLanguageOfInterest();
    if (!selectedLanguageId) return '';

    const teachingLanguages = this.teachingLanguages$();
    if (!teachingLanguages?.teachingLanguages) return '';

    const selectedLanguage = teachingLanguages.teachingLanguages.find(
      lang => lang.id === selectedLanguageId
    );

    return selectedLanguage?.name || '';
  }

  getStudentLevelName(): string {
    const selectedLevel = this.groupForm.controls['studentLevel']?.value;
    if (!selectedLevel) return '';

    const levelOption = this.studentLevels.find(level => level.code === selectedLevel);
    return levelOption?.name || '';
  }

  getLanguageLevelDisplayName(level: ILanguageLevelsEnum): string {
    switch (level) {
      case ILanguageLevelsEnum.A1: return 'A1';
      case ILanguageLevelsEnum.A2: return 'A2';
      case ILanguageLevelsEnum.B1: return 'B1';
      case ILanguageLevelsEnum.B2: return 'B2';
      case ILanguageLevelsEnum.C1: return 'C1';
      case ILanguageLevelsEnum.C2: return 'C2';
      case ILanguageLevelsEnum.None: return 'N/A';
      default: return 'N/A';
    }
  }

  /** Initializes student levels from enum */
  private initializeStudentLevels(): void {
    const config: GenericPrimeEnumToDropdownOptionsConfig = {
      labelProperty: 'name',
      valueProperty: 'code',
      excludeKeys: ['All'],
      additionalProperties: {
        description: (key: string) => `Level ${key}`
      }
    };
    this.studentLevels = this.services.general.getDropdownOptionsFromEnum(IStudentLevelEnum, config);
  }

  private initEvents() {
    this.services.eventBus.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    this.loadStudents();
  }

  loadStudents() {
    // Load all students for the selected language without level filtering
    // Level filtering will be handled by the smart student selector
    this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentStudents, new DefaultGetStudentsRequest({
      teachingLanguage: this.selectedLanguageOfInterest(),
      // Remove level filtering to get all students
    })));
  }

  changeGroupState(state: GroupDialogState) {
    this.editGroupState.set(state);
    this.isPanelClosed = false;
    this.onGroupStateChanged.emit(state);
  }

  private handleGetAvailabilitySuccess(response: IGetAvailabilityResponse): void {
    this.availabilityTimezone = {
      timeZoneDisplayName: response.availability.timeZoneDisplayName,
      timeZoneIana: response.availability.timeZoneIana,
    };
    this.availabilityDataLoaded.set(true);
    this.availabilityData.set(response.availability.weekDayTimeSlots);

    this.editGroupForm.patchValue({
      availability: {
        availabilityId: response.availability.availabilityId,
        weekDayTimeSlots: response.availability.weekDayTimeSlots,
        timeZoneDisplayName: response.availability.timeZoneDisplayName,
        timeZoneId: response.availability.timeZoneId,
        timeZoneIana: response.availability.timeZoneIana,
      }
    });

    console.log("🚀 ~ StudentGroupSelectionAvailabilityStepComponent ~ handleGetAvailabilitySuccess ~ availabilityData:", this.services.studentGroup.getEditStudentGroupRequest());
  }

  // Handler Methods
  private handleFetchSuccess(response: IFindCommonTimeSlotsResponse): void {
    console.log(response);
    this.availabilityDataLoaded.set(true);
    if (!response) {
      this.areAllCommonTimeSlotsEmptyBool.set(true);
      this.availabilityData.set([]);
      return;
    }
    this.availabilityData.set(response.availability.weekDayTimeSlots);
    this.areAllCommonTimeSlotsEmptyBool.set(this.areAllCommonTimeSlotsEmpty(response.availability.weekDayTimeSlots));
  }



  private handleQueryParams(): void {
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      const groupIdFromParams = params['groupId'] || null;
      this.groupId.set(groupIdFromParams);

      if (groupIdFromParams) {
        this.isEditMode.set(true);
        this.loadGroupDetails(groupIdFromParams);
        this.editGroupState.set(GroupDialogState.EditGroup);
      } else {
        this.isEditMode.set(false);
        this.editGroupState.set(GroupDialogState.CreateGroupSuggestionStep);
      }
    });
  }

  private loadGroupDetails(groupId: string): void {
    this.services.api.getApiData<IGetStudentGroupResponse>(
      { url: StudentGroupRoutes.getStudentGroup, method: 'GET' },
      { GroupId: groupId }
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: this.handleGetGroupSuccess,
      error: this.handleGetGroupError
    });
  }

  private handleGetGroupSuccess = (response: { studentGroup: any }): void => {
    this.studentGroupItem.set(response.studentGroup);

    console.log('🚀 ~ ParentOverviewGroupsCreateComponent ~ handleGetGroupSuccess ~ response:', response);

    // Set the selected language first (needed for student loading)
    if (response.studentGroup.teachingLanguageId) {
      this.selectedLanguageOfInterest.set(response.studentGroup.teachingLanguageId);
      // Load students for the group's teaching language
      // this.loadStudents();
    }

    console.log('response', response.studentGroup);
    // Populate form with existing group data
    this.editGroupForm.patchValue({
      parentId: this.services.auth.getUserClaims().id,
      groupId: response.studentGroup.id,
      groupName: response.studentGroup.groupName,
      studentLevel: response.studentGroup.studentLevel!,
    });


    // Explicitly set the studentLevel control value to ensure p-select updates
    const studentLevelControl = this.editGroupForm.get('studentLevel');
    if (studentLevelControl && response.studentGroup.studentLevel) {
      studentLevelControl.setValue(response.studentGroup.studentLevel);
      studentLevelControl.markAsDirty();
      studentLevelControl.updateValueAndValidity();
      console.log('studentLevelControl', studentLevelControl?.value);
    }

    // Trigger change detection to ensure UI updates

    this.availabilityDataLoaded.set(false);

    // Set selected students and store original students for edit mode tracking
    if (response.studentGroup.basicProfileInfoDto) {
      const originalStudents = response.studentGroup.basicProfileInfoDto!;
      this.selectedStudents.set(originalStudents);
      this.originalStudents.set(originalStudents); // Store original students for comparison

      // Initialize form with original students (no changes yet)
      this.editGroupForm.patchValue({
        studentsToAdd: [], // Start with empty - will be populated when students are actually added
        studentsToRemove: [] // Start with empty - will be populated when students are actually removed
      });
    }

    // Load availability data if available
    if (response.studentGroup.availabilityId) {
      this.getGroupAvailability(response.studentGroup.availabilityId);
    }
  }

  private handleGetGroupError = (error: any): void => {
    console.error('Failed to load group details', error);
    this.services.toast.show({ severity: 'error', summary: 'Error', detail: 'Failed to load group details' });
    this.services.router.navigate(['/dashboard/parent/groups']);
  }
}
