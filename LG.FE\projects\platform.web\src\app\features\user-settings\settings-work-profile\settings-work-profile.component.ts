import { CommonModule } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DisableWhileRequestDirective } from '@platform.app/core/directives/loading-button.directive';
import { IAttachments, IDownloadAttachmentRequest, IDownloadAttachmentResponse, ITeacherStudentAgesPreferenceEnum, SkeletonLoaderComponent } from 'SharedModules.Library';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { MultiSelectModule } from 'primeng/multiselect';
import { ProgressBarModule } from 'primeng/progressbar';
import { SelectModule } from 'primeng/select';
import { TextareaModule } from 'primeng/textarea';
import { TooltipModule } from 'primeng/tooltip';
import { delay, Observable, Observer, Subscription } from 'rxjs';
import {
  AuthStateService,
  CustomValidators,
  DataApiStateService,
  EmitEvent,
  EventBusService,
  Events,
  FileValidationOptions,
  FileValidationService,
  FormFieldValidationMessageComponent,
  GeneralService,
  getToastMessage,
  HandleApiResponseService,
  IGetTeacherWorkProfileRequest,
  IGetTeacherWorkProfileResponse,
  ILanguageLevelsEnum,
  IPatchUpdateTeacherWorkProfileRequest,
  IProfileInfo,
  IRegTeacherPaymentsDto,
  IRegTeacherWorkProfileDto,
  ITeacherTeachingLanguageDto,
  ITeachingLanguageDto,
  IAttachmentDto,
  PrimeReactiveFormInputComponent,
  Severity,
  ToastMessages,
  ToastService,
  untilDestroyed,
  IHttpStatusCode,
  ITeacherStudentAgesExperienceEnum,
  IPatchUpdateTeacherWorkProfileResponse,
  FormErrorScrollerService
} from 'SharedModules.Library';

const allStudentAgesValue = 31;

@Component({
  selector: 'app-settings-work-profile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PrimeReactiveFormInputComponent,
    FormFieldValidationMessageComponent,
    TextareaModule,
    FileUploadModule,
    TooltipModule,
    SelectModule,
    ButtonModule,
    ProgressBarModule,
    MultiSelectModule,
    SkeletonLoaderComponent,
    DisableWhileRequestDirective
  ],
  templateUrl: './settings-work-profile.component.html',
  styleUrl: './settings-work-profile.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [],
})
export class SettingsWorkProfileComponent {
  private readonly untilDestroyed = untilDestroyed();
  readonly Severity = Severity;

  // Injected Services
  readonly generalService = inject(GeneralService);
  private readonly eventBusService = inject(EventBusService);
  private readonly handleApiService = inject(HandleApiResponseService);
  private readonly authStateService = inject(AuthStateService);
  private readonly dataStateService = inject(DataApiStateService);
  private readonly fileValidationService = inject(FileValidationService);
  private readonly formErrorScroller = inject(FormErrorScrollerService);
  private readonly toastService = inject(ToastService);
  private readonly fb = inject(FormBuilder);

  // Signals
  formGetDataLoaded = signal<boolean>(false);
  formPostDataIsUploading = signal<boolean>(false);
  progressUploadValue = signal<number>(0);

  // Form
  form: FormGroup = {} as FormGroup;
  submitRequest$!: Subscription;

  // State
  invalidFields: string[] = [];
  attachmentIdsToRemove: string[] = [];
  profilePhotoFile: Blob | null = null;
  uploadedFiles: File[] = [];
  uploadedDegreeFiles: File[] = [];
  uploadedCvFile: any | null = null;
  uploadedCoverLetterFile: any | null = null;
  selectedLanguage: ITeachingLanguageDto | null = null;
  selectedLevel: { name: string; code: ILanguageLevelsEnum } | null = null;
  studentAgesOptions: { label: string; value: string }[] = [];
  studentAgesPreferenceOptions: { label: string; value: string }[] = [];

  // Computed Properties
  teachingLanguages$ = computed(() => this.dataStateService.teachingLanguages.state() || []);
  studentLevels$ = computed<{ name: string; code: ILanguageLevelsEnum }[]>(() => {
    return Object.keys(ILanguageLevelsEnum)
      .filter((key) => isNaN(Number(key)) && key !== 'None')
      .map((key) => ({
        name: key,
        code: ILanguageLevelsEnum[key as keyof typeof ILanguageLevelsEnum],
      }));
  });

  constructor() { }

  ngOnInit(): void {
    this.loadTeachingLanguages();
    this.createForm();
    this.fetchTeacherWorkProfile();
    this.initStudentAgesOptions();
    this.initStudentAgesPreferenceOptions();
    this.form.statusChanges.pipe(this.untilDestroyed()).subscribe(() => {
      this.invalidFields = CustomValidators.findInvalidControls(this.form);
    });
  }

  fetchTeacherWorkProfile(): void {
    this.formGetDataLoaded.set(false);
    this.handleApiService
      .getApiData<IGetTeacherWorkProfileResponse>(
        { url: IProfileInfo.getTeacherWorkProfile, method: 'GET' },
        { TeacherId: this.authStateService.getUserClaims().id }
      )
      .subscribe({
        next: (response: IGetTeacherWorkProfileResponse) => {
          this.patchFormWithResponse(response);
          this.formGetDataLoaded.set(true);
        },
        error: () => {
          this.toastService.show(getToastMessage(ToastMessages.UserSettingsSaved.error, {}));
        },
      });
  }

  private convertEnumFlagsToArray(flags: number): string[] {
    const result: string[] = [];
    const enumValues = [
      ITeacherStudentAgesExperienceEnum.TowToFour,
      ITeacherStudentAgesExperienceEnum.FourToSix,
      ITeacherStudentAgesExperienceEnum.SixToEight,
      ITeacherStudentAgesExperienceEnum.EightToTen,
      ITeacherStudentAgesExperienceEnum.AboveTen,
    ];
    const allOptionsValue = allStudentAgesValue;

    if (flags === allOptionsValue) {
      return enumValues.map((val) => val.toString());
    }

    for (const value of enumValues) {
      if (flags & value) {
        result.push(value.toString());
      }
    }
    return result;
  }

  patchFormWithResponse(response: IGetTeacherWorkProfileResponse): void {
    const { workProfile, payments } = response;
    const studentAgesExperienceArray = this.convertEnumFlagsToArray(workProfile.studentAgesExperience);
    const studentAgesPreferenceArray = this.convertEnumFlagsToArray(workProfile.studentAgesPreference!);

    this.form.patchValue({
      workProfile: {
        teachingMethods: workProfile.teachingMethods,
        yearsOfTeaching: workProfile.yearsOfTeaching,
        studentAgesExperience: studentAgesExperienceArray,
        studentAgesPreference: studentAgesPreferenceArray,
      },
      payments: {
        stripeAccount: payments.stripeAccount,
      },
    });

    workProfile.teachingLanguages?.forEach((lang: ITeacherTeachingLanguageDto) => {
      const languageGroup = this.fb.group({
        teachingLanguageId: [lang.teachingLanguageId],
        teachingLanguageName: [lang.teachingLanguageName],
        languageLevelsEnum: [lang.languageLevelsEnum],
      });
      this.teachingLanguagesArray.push(languageGroup);
    });

    this.bindFileUploads(workProfile);
  }

  bindFileUploads(workProfile: any): void {
    if (workProfile.degreesDisplayDto?.length > 0) {
      this.uploadedDegreeFiles = workProfile.degreesDisplayDto.map((degree: IAttachmentDto) => ({
        id: degree.id,
        name: degree.originalFileName,
        size: degree.sizeInKB * 1024,
        type: degree.contentType,
      }));
      this.degreesArray.clear();
      this.uploadedDegreeFiles.forEach((file) => this.degreesArray.push(this.fb.control(file)));
    }

    if (workProfile.cvDisplayDto) {
      this.uploadedCvFile = {
        id: workProfile.cvDisplayDto.id,
        name: workProfile.cvDisplayDto.originalFileName,
        size: workProfile.cvDisplayDto.sizeInKB * 1024,
        type: workProfile.cvDisplayDto.contentType,
      };
      this.cvControl.setValue(this.uploadedCvFile);
    }

    if (workProfile.coverLetterDisplayDto) {
      this.uploadedCoverLetterFile = {
        id: workProfile.coverLetterDisplayDto.id,
        name: workProfile.coverLetterDisplayDto.originalFileName,
        size: workProfile.coverLetterDisplayDto.sizeInKB * 1024,
        type: workProfile.coverLetterDisplayDto.contentType,
      };
      this.coverLetterControl.setValue(this.uploadedCoverLetterFile);
    }
  }

  get teachingLanguagesArray(): FormArray {
    return this.form.get('workProfile.teachingLanguages') as FormArray;
  }

  get teachingLanguagesControls(): FormGroup[] {
    return this.teachingLanguagesArray.controls as FormGroup[];
  }

  get degreesArray(): FormArray {
    return this.form.get('workProfile.degrees') as FormArray;
  }

  get cvControl(): FormControl {
    return this.form.get('workProfile.cv') as FormControl;
  }

  get coverLetterControl(): FormControl {
    return this.form.get('workProfile.coverLetter') as FormControl;
  }

  get studentAgesExperienceControl(): FormControl {
    return this.form.get('workProfile.studentAgesExperience') as FormControl;
  }

  get studentAgesPreferenceControl(): FormControl {
    return this.form.get('workProfile.studentAgesPreference') as FormControl;
  }

  createForm(): void {
    this.form = this.fb.group({
      workProfile: this.fb.group({
        teachingLanguages: this.fb.array<ITeacherTeachingLanguageDto>([], [Validators.required]),
        teachingMethods: this.fb.control<string>('', [Validators.required]),
        yearsOfTeaching: this.fb.control<number | null>(1, [Validators.required, Validators.min(1)]),
        studentAgesExperience: this.fb.control<ITeacherStudentAgesExperienceEnum[]>(
          [],
          [Validators.required]
        ),
        studentAgesPreference: this.fb.control<ITeacherStudentAgesPreferenceEnum[]>(
          [],
          [Validators.required]
        ),
        degrees: this.fb.array<IAttachmentDto>([], [Validators.required]),
        cv: this.fb.control<IAttachmentDto | null>(null, [Validators.required]),
        coverLetter: this.fb.control<IAttachmentDto | null>(null, [Validators.required]),
      }),
      payments: this.fb.group<IRegTeacherPaymentsDto>(
        {
          stripeAccount: '',
        },
        // { validators: CustomValidators.atLeastOnePaymentMethodValidator }
      ),
    });
  }

  onSubmit(): void {
    const formData = new FormData();
    const formValue = this.form.value;

    if (!this.formErrorScroller.validateAndScrollToFirstError(this.form)) {
      console.log('Form is invalid');
      return;
    }

    this.appendWorkProfile(formData, formValue.workProfile);
    this.appendPayments(formData, formValue.payments);

    formData.append('teacherId', this.authStateService.getUserClaims().id);
    this.attachmentIdsToRemove.forEach((id, index) => {
      formData.append(`attachmentIdsToRemove[${index}]`, id);
    });

    this.progressUploadValue.set(0);
    this.formGetDataLoaded.set(false);
    this.formPostDataIsUploading.set(true);
    this.generalService.showDivLoading('.work_profile_form');

    this.submitRequest$ = this.handleApiService
      .sendMultipartFormData<IPatchUpdateTeacherWorkProfileRequest>(IProfileInfo.patchTeacherWorkProfile, formData, 'POST')
      .subscribe({
        next: (response: Partial<{ progress: number; response?: HttpResponse<IPatchUpdateTeacherWorkProfileResponse> }>) => {
          console.log(response);
          if (response.response) {
            if (response.response.status === IHttpStatusCode.NoContent) {
              this.generalService.hideDivLoading();
              this.formPostDataIsUploading.set(false);
              this.formGetDataLoaded.set(true);
              this.fetchTeacherWorkProfile();
              // setTimeout(() => window.scrollTo(0, 0), 100);
              this.toastService.show(getToastMessage(ToastMessages.UserSettingsSaved.success, {}));
            }
          } else {
            this.progressUploadValue.set(response.progress || 0);
          }
        },
        error: (error) => {
          this.formPostDataIsUploading.set(false);
          this.formGetDataLoaded.set(true);
          this.generalService.hideDivLoading();
          console.error('Failed to update teacher:', error);
        },
      });
  }

  private appendPayments(formData: FormData, payments: IRegTeacherPaymentsDto): void {
    this.appendObjectFields(formData, 'payments', {
      stripeAccount: payments.stripeAccount || '',
    });
  }

  private appendWorkProfile(formData: FormData, workProfile: IRegTeacherWorkProfileDto): void {
    const studentAgesExperienceArray = this.studentAgesExperienceControl.value as string[];
    const studentAgesExperienceValue = this.convertArrayToEnumFlags(studentAgesExperienceArray);
    const studentAgesPreferenceArray = this.studentAgesPreferenceControl.value as string[];
    const studentAgesPreferenceValue = this.convertArrayToEnumFlags(studentAgesPreferenceArray);

    workProfile.teachingLanguages?.forEach((lang: ITeacherTeachingLanguageDto, index: number) => {
      this.appendObjectFields(formData, `workProfile.teachingLanguages[${index}]`, {
        teachingLanguageId: lang.teachingLanguageId || '',
        teachingLanguageName: lang.teachingLanguageName || '',
        languageLevelsEnum: JSON.stringify(lang.languageLevelsEnum) || '',
      });
    });

    this.appendObjectFields(formData, 'workProfile', {
      teachingMethods: workProfile.teachingMethods || '',
      yearsOfTeaching: workProfile.yearsOfTeaching?.toString() || '0',
      studentAgesExperience: studentAgesExperienceValue.toString(),
      studentAgesPreference: studentAgesPreferenceValue.toString(),
    });

    if (workProfile.degrees && Array.isArray(workProfile.degrees)) {
      workProfile.degrees.forEach((degree: File) => {
        if (degree instanceof File) {
          formData.append('workProfile.degrees', degree, degree.name);
        }
      });
    }

    if (workProfile.cv instanceof File) {
      formData.append('workProfile.cv', workProfile.cv, workProfile.cv.name);
    }

    if (workProfile.coverLetter instanceof File) {
      formData.append('workProfile.coverLetter', workProfile.coverLetter, workProfile.coverLetter.name);
    }
  }

  private loadTeachingLanguages(): void {
    this.eventBusService.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    this.eventBusService.emit(new EmitEvent(Events.StateLoadNativeLanguages, undefined));
  }

  addTeachingLanguage(): void {
    if (!this.selectedLanguage || !this.selectedLevel) return;

    const languageGroup = this.fb.group({
      teachingLanguageId: [this.selectedLanguage.id],
      teachingLanguageName: [this.selectedLanguage.name],
      languageLevelsEnum: [this.selectedLevel.code],
    });
    this.teachingLanguagesArray.push(languageGroup);
    this.selectedLanguage = null;
    this.selectedLevel = null;
  }

  removeTeachingLanguage(index: number): void {
    this.teachingLanguagesArray.removeAt(index);
    this.selectedLanguage = null;
    this.selectedLevel = null;
  }

  onLanguageOfInterestChange(event: { value: ITeachingLanguageDto }): void {
    this.selectedLanguage = event.value;
  }

  onSelectedLanguageLevel(event: { name: string; code: ILanguageLevelsEnum }): void {
    this.selectedLevel = event;
  }

  onDegreesFileSelect(event: { currentFiles: File[] }): void {
    const selectedFiles = event.currentFiles;
    const fileOptions = FileValidationOptions.create().allowWord().allowPdf().withMaxSize(5);

    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    selectedFiles.forEach((file) => {
      const validationResult = this.fileValidationService.validateFile(file, fileOptions);
      if (validationResult.isValid) {
        validFiles.push(file);
        this.degreesArray.push(this.fb.control(file));
      } else {
        invalidFiles.push(`${file.name}: ${validationResult.errors.join(', ')}`);
      }
    });

    this.uploadedDegreeFiles = validFiles;
    this.degreesArray.clear();
    validFiles.forEach((file) => this.degreesArray.push(this.fb.control(file)));

    if (invalidFiles.length > 0) {
      this.toastService.show(
        getToastMessage(ToastMessages.UserSettingsSavePassword.error, {
          data: `The following files failed validation:\n${invalidFiles.join('\n')}`,
        })
      );
    }
  }

  removeDegreeFile(file: any, index: number): void {
    if (file.id) {
      this.attachmentIdsToRemove.push(file.id);
    }
    this.uploadedDegreeFiles = this.uploadedDegreeFiles.filter((f) => f !== file);
    this.degreesArray.removeAt(index);
  }

  onCvFileSelect(event: { files: File[] }): void {
    const fileOptions = FileValidationOptions.create().allowWord().allowPdf().withMaxSize(5);
    const validationResult = this.fileValidationService.validateFile(event.files[0], fileOptions);
    if (validationResult.isValid) {
      const selectedFile = event.files[0];
      if (this.uploadedCvFile && (this.uploadedCvFile as IAttachmentDto).id) {
        this.attachmentIdsToRemove.push((this.uploadedCvFile as IAttachmentDto).id);
      }
      this.uploadedCvFile = selectedFile;
      this.cvControl.setValue(selectedFile);
    } else {
      this.toastService.show(
        getToastMessage(ToastMessages.UserSettingsSavePassword.error, {
          data: validationResult.errors.join('\n'),
        })
      );
    }
  }

  removeCvFile(): void {
    if (this.uploadedCvFile && (this.uploadedCvFile as IAttachmentDto).id) {
      this.attachmentIdsToRemove.push((this.uploadedCvFile as IAttachmentDto).id);
    }
    this.uploadedCvFile = null;
    this.cvControl.setValue(null);
  }

  onCoverLetterFileSelect(event: { files: File[] }): void {
    const selectedFile = event.files[0];
    const fileOptions = FileValidationOptions.create().allowWord().allowPdf().withMaxSize(5);
    const validationResult = this.fileValidationService.validateFile(selectedFile, fileOptions);
    if (validationResult.isValid) {
      if (this.uploadedCoverLetterFile && (this.uploadedCoverLetterFile as IAttachmentDto).id) {
        this.attachmentIdsToRemove.push((this.uploadedCoverLetterFile as IAttachmentDto).id);
      }
      this.uploadedCoverLetterFile = selectedFile;
      this.coverLetterControl.setValue(selectedFile);
    } else {
      this.toastService.show(
        getToastMessage(ToastMessages.UserSettingsSavePassword.error, {
          data: validationResult.errors.join('\n'),
        })
      );
    }
  }

  onStudentAgesChange(event: { value: string[] }): void {
    const selectedValues = event.value;
    const allOptionsValue = allStudentAgesValue.toString();
    const allIndividualOptions = [
      ITeacherStudentAgesExperienceEnum.TowToFour.toString(),
      ITeacherStudentAgesExperienceEnum.FourToSix.toString(),
      ITeacherStudentAgesExperienceEnum.SixToEight.toString(),
      ITeacherStudentAgesExperienceEnum.EightToTen.toString(),
      ITeacherStudentAgesExperienceEnum.AboveTen.toString(),
    ];

    if (selectedValues.includes(allOptionsValue)) {
      this.studentAgesExperienceControl.setValue([...allIndividualOptions], { emitEvent: false });
    } else if (allIndividualOptions.every((val) => selectedValues.includes(val))) {
      this.studentAgesExperienceControl.setValue([...selectedValues, allOptionsValue], { emitEvent: false });
    } else if (selectedValues.includes(allOptionsValue)) {
      this.studentAgesExperienceControl.setValue(
        selectedValues.filter((val) => val !== allOptionsValue),
        { emitEvent: false }
      );
    } else {
      this.studentAgesExperienceControl.setValue(selectedValues, { emitEvent: false });
    }
  }

  onStudentAgesPreferenceChange(event: any): void {
    const selectedValues = event.value;
    console.log(selectedValues);
    this.studentAgesPreferenceControl.setValue(selectedValues);
    this.studentAgesPreferenceControl.markAsTouched();
  }

  private convertArrayToEnumFlags(values: string[]): number {
    let result = 0;
    if (Array.isArray(values)) {
      values.forEach((value) => {
        const numValue = parseInt(value, 10);
        if (!isNaN(numValue)) {
          result |= numValue;
        }
      });
    }
    return result;
  }

  private appendObjectFields(formData: FormData, prefix: string, fields: Record<string, any>): void {
    Object.entries(fields).forEach(([key, value]) => {
      formData.append(`${prefix}.${key}`, value);
    });
  }

  private initStudentAgesPreferenceOptions(): void {
    this.studentAgesPreferenceOptions = [
      { label: '2-4 years', value: ITeacherStudentAgesPreferenceEnum.TowToFour.toString() },
      { label: '4-6 years', value: ITeacherStudentAgesPreferenceEnum.FourToSix.toString() },
      { label: '6-8 years', value: ITeacherStudentAgesPreferenceEnum.SixToEight.toString() },
      { label: '8-10 years', value: ITeacherStudentAgesPreferenceEnum.EightToTen.toString() },
      { label: 'Above 10 years', value: ITeacherStudentAgesPreferenceEnum.AboveTen.toString() }
    ];
  }

  private initStudentAgesOptions(): void {
    this.studentAgesOptions = [
      { label: '2-4 Years', value: ITeacherStudentAgesExperienceEnum.TowToFour.toString() },
      { label: '4-6 Years', value: ITeacherStudentAgesExperienceEnum.FourToSix.toString() },
      { label: '6-8 Years', value: ITeacherStudentAgesExperienceEnum.SixToEight.toString() },
      { label: '8-10 Years', value: ITeacherStudentAgesExperienceEnum.EightToTen.toString() },
      { label: 'Above 10 Years', value: ITeacherStudentAgesExperienceEnum.AboveTen.toString() },
      { label: 'All of the above', value: allStudentAgesValue.toString() },
    ];
  }

  deleteUploadedCoverLetterFile(): void {
    if (this.uploadedCoverLetterFile && (this.uploadedCoverLetterFile as IAttachmentDto).id) {
      this.attachmentIdsToRemove.push((this.uploadedCoverLetterFile as IAttachmentDto).id);
    }
    this.uploadedCoverLetterFile = null;
    this.coverLetterControl.setValue(null);
  }

  download(file: IAttachmentDto, index: number): void {
    console.log('Downloading file...', file);
    this.handleApiService
      .getApiData<IDownloadAttachmentResponse>(
        { url: IAttachments.getDownLoadAttachment, method: 'GET' },
        { id: file.id }
      )
      .subscribe({
        next: (response: IDownloadAttachmentResponse) => {
          if (response.url) {
            // Method 1: Open in a new tab (simple but visible to user)
            window.open(response.url, '_blank');

            // Method 2: Trigger an invisible download
            // const link = document.createElement('a');
            // link.href = response.url;
            // link.style.display = 'none';
            // document.body.appendChild(link);
            // link.click();
            // document.body.removeChild(link);
          } else {
            console.error('No download URL received');
          }
        },
        error: () => {
          this.toastService.show(getToastMessage(ToastMessages.UserSettingsSaved.error, {}));
        },
      });
  }
}