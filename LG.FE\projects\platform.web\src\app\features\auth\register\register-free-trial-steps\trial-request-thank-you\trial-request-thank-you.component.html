@defer (on immediate) {

<!-- Galactic Journey Success Section -->
<div class="galactic-success-container">
  <!-- Animated Success Icon with Cosmic Theme -->
  <div class="success-icon-wrapper">
    <div class="cosmic-glow"></div>
    <div class="success-icon-background">
      <i class="pi pi-check success-icon"></i>
    </div>
    <div class="orbital-ring"></div>
  </div>

  <!-- Galactic Journey Content -->
  <div class="success-content text-center">
    <h2 class="galactic-title mt-3">🚀 Request received successfully!</h2>
    <p class="journey-subtitle">Your {{ getLanguageDisplayName() }} adventure begins now</p>

    <div class="trial-info-galactic">
      <div class="stellar-badge">
        <i class="pi pi-star-fill stellar-icon"></i>
        <span> FREE {{ getLanguageDisplayName() }} Trial Lesson</span>
      </div>
      <!-- <div class="destination-name">{{ getLanguageDisplayName() }} Galaxy</div> -->
    </div>

    <div class="mission-equipment">
      <span class="equipment-item">🛡️ No cost, no risk</span>
      <span class="equipment-item">👨‍🚀 Expert teachers</span>
      <span class="equipment-item">📡 24h support</span>
    </div>
  </div>
</div>

}



<!-- Compact Grouping Section -->
<app-available-students-for-groups-list [studentId]="studentId()!" [teachingLanguageId]="teachingLanguageId()!" [teachingLanguageName]="teachingLanguageName()!"
  (studentSelected)="onStudentSelected($event)">
</app-available-students-for-groups-list>


  <!-- Mission Control Action Center -->
<div class="mission-control-center">
  <!-- Primary Mission Actions -->
  <div class="primary-actions">
    <!-- <p-button [routerLink]="['/dashboard/book-lesson']" [rounded]="true" icon="pi pi-calendar-plus" iconPos="left"
      label="Schedule Lesson" styleClass=" p-button-success p-button-sm galactic-primary">
    </p-button> -->
       <p-button [routerLink]="['/dashboard/parent/overview']" [rounded]="true" icon="pi pi-home" iconPos="left"
      label="Go to Dashboard" styleClass="p-button-outlined p-button-sm">
    </p-button>
    <p-button [routerLink]="['/dashboard/parent/students']" [rounded]="true" icon="pi pi-users" iconPos="left"
      label="View Students" styleClass="p-button-outlined p-button-sm">
    </p-button>

  </div>

  <!-- Cosmic Divider -->
  <!-- <div class="cosmic-divider">
    <div class="divider-line"></div>
    <div class="cosmic-orb">
      OR
    </div>
    <div class="divider-line"></div>
  </div>
  <div class="secondary-actions">
 
  </div> -->
</div>