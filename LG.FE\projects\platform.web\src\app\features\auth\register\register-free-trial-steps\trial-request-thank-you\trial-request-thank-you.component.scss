@use "mixins";

// Galactic Journey Color Palette
$primary: #6366f1;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #10b981;
$success-50: #ecfdf5;
$success-100: #d1fae5;
$success-600: #059669;
$success-700: #047857;

$warning: #f59e0b;
$warning-50: #fffbeb;
$warning-100: #fef3c7;

// Cosmic Color Palette
$cosmic-deep: #0f0f23;
$cosmic-blue: #1e1b4b;
$cosmic-purple: #4c1d95;
$cosmic-indigo: #312e81;
$cosmic-violet: #5b21b6;
$starlight: #333;
$nebula-pink: #ec4899;
$nebula-cyan: #06b6d4;
$cosmic-gold: #fbbf24;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Professional shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Smooth transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
  width: 100%;
}

// Galactic Journey Main Container
.galactic-success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  // background: linear-gradient(135deg, $cosmic-deep 0%, $cosmic-blue 50%, $cosmic-indigo 100%);
  border-radius: 12px;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
  // border: 1px solid rgba(99, 102, 241, 0.3);
  // box-shadow: 0 8px 32px rgba(15, 15, 35, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);

  @include mixins.breakpoint(mobile) {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  // Subtle cosmic shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
  }
}

// Cosmic Success Icon
.success-icon-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 0.75rem;
  z-index: 3;


  .success-icon-background {
    position: relative;
    z-index: 3;
    width: 50px;
    height: 50px;
    background: var(--p-green-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.5), $shadow-md;
    animation: success-bounce 0.8s ease-out;
    border: 2px solid rgba(255, 255, 255, 0.2);

    @include mixins.breakpoint(mobile) {
      width: 40px;
      height: 40px;
    }

    .success-icon {
      font-size: 1.25rem;
      color: white;
      animation: check-draw 0.6s ease-out 0.4s both;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }
  }

  .orbital-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 65px;
    height: 65px;
    border: 1px solid rgba(6, 182, 212, 0.3);
    border-radius: 50%;
    z-index: 2;
    animation: orbital-rotation 8s linear infinite;

    @include mixins.breakpoint(mobile) {
      width: 55px;
      height: 55px;
    }

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background: $nebula-cyan;
      border-radius: 50%;
      box-shadow: 0 0 6px $nebula-cyan;
    }
  }
}

// Galactic Journey Content
.success-content {
  position: relative;
  z-index: 3;

  .galactic-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: $starlight;
    margin: 0 0 0.25rem;
    letter-spacing: -0.025em;
    line-height: 1.2;
    animation: slide-up 0.6s ease-out 0.2s both;
    text-shadow: 0 0 10px rgba(248, 250, 252, 0.5);

    @include mixins.breakpoint(mobile) {
      font-size: 1.125rem;
      margin-bottom: 0.1875rem;
    }
  }

  .journey-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    // color: rgba(248, 250, 252, 0.8);
    margin: 0 0 0.75rem;
    line-height: 1.3;
    animation: slide-up 0.6s ease-out 0.3s both;

    @include mixins.breakpoint(mobile) {
      font-size: 0.8125rem;
      margin-bottom: 0.5rem;
    }
  }
}

// Galactic Trial Info
.trial-info-galactic {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  animation: slide-up 0.6s ease-out 0.4s both;

  @include mixins.breakpoint(mobile) {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .stellar-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    // background: linear-gradient(135deg, $success 0%, $cosmic-gold 100%);
    color: #333;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    // box-shadow: 0 0 15px rgba(16, 185, 129, 0.4), $shadow-sm;
    border: 1px solid rgba(255, 255, 255, 0.2);

    @include mixins.breakpoint(mobile) {
      font-size: 0.6875rem;
      padding: 0.1875rem 0.375rem;
    }

    .stellar-icon {
      font-size: 0.75rem;
      animation: stellar-pulse 2s ease-in-out infinite;
      color: $cosmic-gold;
      text-shadow: 0 0 8px $cosmic-gold;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem;
      }
    }
  }

  .destination-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: $starlight;
    text-transform: capitalize;
    text-shadow: 0 0 8px rgba(248, 250, 252, 0.3);

    @include mixins.breakpoint(mobile) {
      font-size: 1rem;
    }
  }
}

// Mission Equipment
.mission-equipment {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
  animation: slide-up 0.6s ease-out 0.6s both;

  @include mixins.breakpoint(mobile) {
    gap: 0.5rem;
  }

  .equipment-item {
    font-size: 0.75rem;
    font-weight: 600;
    color: $starlight;
    // background: rgba(16, 185, 129, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    // border: 1px solid rgba(16, 185, 129, 0.3);
    // backdrop-filter: blur(4px);
    // box-shadow: 0 0 10px rgba(16, 185, 129, 0.2);

    @include mixins.breakpoint(mobile) {
      font-size: 0.6875rem;
      padding: 0.1875rem 0.375rem;
    }
  }
}

// Mission Control Action Center
.mission-control-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  // margin: 1.5rem 0;
  padding: 1.25rem;
  position: relative;
  overflow: hidden;

  @include mixins.breakpoint(mobile) {
    gap: 0.75rem;
    margin: 1rem 0;
    padding: 1rem;
  }


  .primary-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;

    @include mixins.breakpoint(mobile) {
      gap: 0.5rem;
    }
  }

  .secondary-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;

    @include mixins.breakpoint(mobile) {
      gap: 0.5rem;
    }
  }
}

// Cosmic Divider
.cosmic-divider {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 200px;
  gap: 0.5rem;

  .divider-line {
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.5), transparent);
  }

  .cosmic-orb {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color:#fff;
    font-size: 0.675rem;

    i {
      font-size: 0.75rem;
      color: white;
      text-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
    }
  }
}

// Compact Grouping Section
.grouping-section-compact {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: $shadow-sm;
  border: 1px solid $gray-200;
  animation: slide-up 0.6s ease-out 1s both;

  @include mixins.breakpoint(mobile) {
    margin-top: 1rem;
    padding: 0.75rem;
  }

  .grouping-header-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    text-align: left;

    @include mixins.breakpoint(mobile) {
      gap: 0.375rem;
      margin-bottom: 0.75rem;
    }

    i {
      font-size: 1rem;
      flex-shrink: 0;

      @include mixins.breakpoint(mobile) {
        font-size: 0.875rem;
      }
    }

    .grouping-title-compact {
      font-size: 0.875rem;
      font-weight: 600;
      color: $gray-700;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }
  }
}

// Galactic Animations
@keyframes cosmic-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes cosmic-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes orbital-rotation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes planet-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes stellar-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

@keyframes success-bounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes check-draw {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Removed particle-float animation as particles were removed for compact design

@keyframes star-twinkle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

