<!-- Table Header -->
 <ng-container *ngIf="showHeader">

  <tr class="p-datatable-thead-row" [ngClass]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      class="p-datatable-thead-cell"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns -->
    <th
      *ngFor="let col of columns"
      class="p-datatable-thead-cell"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">

      <div class="flex align-items-center justify-content-start gap-1">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
      </div>
    </th>
  </tr>
</ng-container>

<!-- Table Footer -->
 <ng-container *ngIf="showFooter">
  <tr class="p-datatable-tfoot-row" [ngClass]="customRowClass">
    <!-- Actions Column (Frozen Left) -->
    <th
      *ngIf="showActionsColumn"
      class="p-datatable-tfoot-cell"
      alignFrozen="left"
      pFrozenColumn
      [style.width]="actionsColumnWidth">
      {{ actionsColumnHeader }}
    </th>

    <!-- Dynamic Columns -->
    <th
      *ngFor="let col of columns"
      class="p-datatable-tfoot-cell"
      pReorderableColumn
      [pSortableColumn]="isColumnSortable(col) ? col.field : undefined"
      [attr.style]="getColumnStyle(col)"
      [ngClass]="{ 'reorderable-column': isColumnReorderable() }">

      <div class="flex align-items-center justify-content-start gap-1">
      {{ col.header }}
      <p-sortIcon
        *ngIf="isColumnSortable(col)"
        [field]="col.field">
      </p-sortIcon>
      </div>
    </th>
  </tr>
  </ng-container>
