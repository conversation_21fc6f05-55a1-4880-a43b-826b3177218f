import {
  ChangeDetectorRef,
  Component,
  ComponentRef,
  computed,
  DestroyRef,
  ElementRef,
  HostListener,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  Renderer2,
  signal,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router, RouterOutlet } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { MessageService } from 'primeng/api';
import { AuthStateService, EmitEvent, EventBusService, Events, GeneralService, MyPreset, TopbarComponent, TopbarSidebarComponent } from 'SharedModules.Library';
import { filter, map } from 'rxjs';
import { ToastModule } from 'primeng/toast';
import { StateApiCallsComponent } from 'SharedModules.Library';
import { LoaderDirective } from './core/directives/loader.directive';
import { CommonModule } from '@angular/common';
import { PrimeNG } from 'primeng/config';
import { ToastComponent, ToastService } from 'SharedModules.Library';


const components = [TopbarComponent, ToastComponent, StateApiCallsComponent];

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    ButtonModule,
    ToastModule,
    components,
    TopbarSidebarComponent,
    LoaderDirective,
  ],
  providers: [],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  router = inject(Router);
  renderer = inject(Renderer2);
  title = 'LG.FE.ELS';
  sidebarVisible = false;
  generalService = inject(GeneralService);
  authService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  @ViewChild('dynamicComponentContainer', {
    read: ViewContainerRef,
    static: true
  }) dynamicComponentContainer: ViewContainerRef | undefined;
  _stable = false;
  loader = computed(() => {
    return this.generalService.routerLoading();
  });
  divLoader = computed(() => {
    return this.generalService.divLoading();
  });
  isDarkMode = signal(false); // Track dark mode state
  loading = signal(false);

  toggleLoading(): void {
    if (this.generalService.divLoading().isLoading) {

      this.generalService.divLoading.set({ isLoading: false, targetSelector: '.basic_info__container', });
    } else {
      this.generalService.divLoading.set({ isLoading: true, targetSelector: '.basic_info__container' });
    }
  }

  private primeConfig = inject(PrimeNG);
  private navigationStartTime: number = 0;

  constructor(private message: MessageService, private toastService: ToastService,
    private elementRef: ElementRef,
    private cdr: ChangeDetectorRef) {

    this.primeConfig.theme.set({
      preset: MyPreset,
      options: {
        //TODO: add real dark mode selector
        darkModeSelector: '.fake-dark-selector', // trying to also force a non-usage of the dark mode
        cssLayer: {
          name: 'primeng',
          order: 'primeng'
        }
      }
    });


    this.router.events.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(event => {
      if (event instanceof NavigationStart) {
        console.log('NavigationStart', event);
        this.navigationStartTime = Date.now();

        this.generalService.showDivLoading();

        this.generalService.routerLoading.set(true);
      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {

        this.generalService.onSidebarVisibleChange(false);
        this.generalService.calendarSidebarVisible.set(false);
        setTimeout(() => {
          this.generalService.setErrorDataSignal('');
          this.generalService.routerLoading.set(false);
          this.generalService.hideDivLoading();
        }, 100);
        const elapsedTime = Date.now() - this.navigationStartTime;
        // console.log(`Navigation took ${elapsedTime} ms`);
        this.renderer.listen('window', 'load', () => {
        });
        // console.log('NavigationEnd', event);
      }
    });
  }

  ngOnInit() {
    this.primeConfig.ripple.set(true);
    this.generalService.openDialog$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(object => {
      if (!object) {
        return;
      }
      console.log(object);
      // Logic to handle opening the dialog with the received parameters
      this.loadDialogComponent((object as any).component, (object as any).parameters);
    });
    this.generalService.setDevice();
    this.listenForClicks();

    // const savedMode = localStorage.getItem('darkMode');
    // this.isDarkMode.set(savedMode === 'true');

    // this.toggleDarkModeClass();


    const user = this.authService.getUser();
    if (!user) {
      return;
    } else {
      // this.eventBusService.emit(new EmitEvent(Events.UserLoggedIn, {}));
    }
    const navigationStart$ = this.router.events.pipe(
      filter(event => event instanceof NavigationStart),
      map(() => {
        this.navigationStartTime = Date.now();
      }));

    // this._zone.runOutsideAngular(() => {
    //   // Check very regularly to see if the pending macrotasks have all cleared
    //   interval(10)
    //     .pipe(
    //       startWith(0), // So that we don't initially wait
    //       // To prevent a memory leak on two closely times route changes, take until the next nav start
    //       takeUntil(navigationStart$),
    //       // Turn the interval number into the current state of the zone
    //       map(() => !this._zone.hasPendingMacrotasks),
    //       // Don't emit until the zone state actually flips from `false` to `true`
    //       distinctUntilChanged(),
    //       // Filter out unstable event. Only emit once the state is stable again
    //       filter(stateStable => stateStable === true),
    //       // Complete the observable after it emits the first result
    //       take(1),
    //       tap(stateStable => {
    //         // FULLY RENDERED!!!!
    //         // Add code here to report Fully Rendered
    //         console.log('Fully Rendered');
    //       })
    //     ).subscribe();
    // });

  }

  ngOnDestroy(): void {
    // Cleanup is handled automatically by takeUntilDestroyed
  }

  toggleDarkMode(): void {
    this.isDarkMode.set(!this.isDarkMode());
    this.toggleDarkModeClass();
    localStorage.setItem('darkMode', this.isDarkMode().toString()); // Persist preference
    this.cdr.detectChanges(); // Ensure UI updates
    this.toastService.show({
      severity: 'info',
      summary: 'Theme Changed',
      detail: `Switched to ${this.isDarkMode() ? 'Dark' : 'Light'} Mode`,
    });
  }

  private toggleDarkModeClass(): void {
    const hostElement = this.elementRef.nativeElement.ownerDocument.body;
    if (this.isDarkMode()) {
      hostElement.classList.add('dark');
    } else {
      hostElement.classList.remove('dark');
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.generalService.setDevice();
  }

  onShowSidebar(showSidebar: boolean) {
    this.sidebarVisible = showSidebar;
  }

  loadDialogComponent(
    component?: any,
    parameters?: any
  ): ComponentRef<any> {
    // Clear previous dynamic component, if any
    this.dynamicComponentContainer!.clear();

    // Resolve the component factory for the provided component type
    // Create the component and attach it to the view
    const componentRef = this.dynamicComponentContainer!.createComponent(component, {
      injector: Injector.create({
        providers: [
          { provide: 'dialogParameters', useValue: parameters }
        ]
      })
    });

    // Optionally, you can return the component reference or any other data
    return componentRef;
  }


  private listenForClicks() {

    this.renderer.listen('document', 'click', (event) => {
      const target = event.target as HTMLElement;
      // console.log(target);

      // Check if the target has the data-closeSidebar attribute
      if (target.hasAttribute('data-closeSidebar')) {
        this.generalService.onSidebarVisibleChange(false);
      }

      if (target.tagName === 'BUTTON') {
        // this.generalService.setErrorDataSignal('');
      }
    });
  }
}
