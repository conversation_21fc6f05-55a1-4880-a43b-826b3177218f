import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, Output, signal, computed, input, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ButtonModule } from 'primeng/button';
import { Password, PasswordModule } from 'primeng/password';
import { AuthStateService, CustomValidators, FormFieldValidationMessageComponent, GeneralService, getToastMessage, HandleApiResponseService, IdentityRoutes, ISetPasswordRequest, ISetPasswordResponse, Severity, ToastMessages, ToastService, untilDestroyed } from 'SharedModules.Library';

import { PasswordStrengthIndicatorComponent } from '../password-strength-indicator/password-strength-indicator.component';

@Component({
  selector: 'app-set-password-stepper',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    PasswordModule,
    FormFieldValidationMessageComponent,
    PasswordStrengthIndicatorComponent
],
  templateUrl: './set-password-stepper.component.html',
  styleUrl: './set-password-stepper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetPasswordStepperComponent {
  authService = inject(AuthStateService);
  generalService = inject(GeneralService);
  apiService = inject(HandleApiResponseService);
  toastService = inject(ToastService);
  fb = inject(FormBuilder);
  Severity = Severity;
  @Input() userId = this.authService.getUserClaims().userId;
  @Output() setPasswordCompleted = new EventEmitter<ISetPasswordResponse>();
  @Output() canceled = new EventEmitter<void>();
  @ViewChild('newPwdInput') newPwdInput!: Password;
  myForm: FormGroup = new FormGroup({});
  currentStep = 1; // Not a signal since it’s not bound to template
  invalidFields: string[] = [];
  private untilDestroyed = untilDestroyed();
  submitted = signal(false);

  constructor() {
  }

  ngOnInit() {
    this.myForm = this.fb.group({
      password: [null, [Validators.required, CustomValidators.passwordStrongCheck]],
      confirmPassword: [null, [Validators.required, CustomValidators.confirmPasswordFirstTime]],
      userId: [this.userId, [Validators.required]],
    });
    this.myForm.valueChanges.pipe(this.untilDestroyed()).subscribe(() => {
      Object.keys(this.myForm.controls).forEach((key) => {
        this.myForm.get(key)?.updateValueAndValidity({ emitEvent: false });
      });
    });

    this.myForm.statusChanges.pipe(this.untilDestroyed()).subscribe(() => {
      this.invalidFields = this.generalService.findInvalidControls(this.myForm);
    });
  }

  ngAfterViewChecked() {
    if (this.newPwdInput) {
      this.newPwdInput.input.nativeElement.autocomplete = 'new-password';
    }
  }

  ngOnDestroy() {
    this.resetForm();
  }

  submitPassword(): void {
    this.submitted.set(true);
    this.invalidFields = this.generalService.findInvalidControls(this.myForm);
    if (this.myForm.valid) {
      this.apiService.getApiData<ISetPasswordResponse>(
        { url: IdentityRoutes.postSetPassword, method: 'POST' },
        this.myForm.value as ISetPasswordRequest
      ).subscribe({
        next: (response: ISetPasswordResponse) => {
          this.resetForm();
          if (response.refreshToken && response.token) {
            this.authService.handleUserDataAndDecodeJWT(response);
          }
          this.toastService.show(ToastMessages.UserSettingsSavePassword.success);
          this.setPasswordCompleted.emit(response);
        },
        error: (error) => {
          this.resetForm();
          this.toastService.show(getToastMessage(ToastMessages.UserSettingsSavePassword.error, { data: error.messages.join(', ') }));
          this.canceled.emit();
        },
      });
    }
  }

  private resetForm(): void {
    this.myForm.reset();
    this.currentStep = 1;
  }

}