import {CommonModule} from '@angular/common';
import {ChangeDetectionStrategy, Component, Input, type OnInit, ViewChild} from '@angular/core';
import {ChartComponent, NgApexchartsModule} from 'ng-apexcharts';
import {ChartOptions} from '../radial-bar-chart/radial-bar-chart.component';

@Component({
    selector: 'app-basic-radialbar-chart',
    imports: [
        CommonModule,
        NgApexchartsModule,
    ],
    templateUrl: './basic-radialbar-chart.component.html',
    styleUrl: './basic-radialbar-chart.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class BasicRadialbarChartComponent implements OnInit {
  @Input() height: number = 280;
  @ViewChild("chart") chart: ChartComponent = {} as ChartComponent;
  public chartOptions: any = {
    chart: {
      height: this.height,
      type: "radialBar"
    }
  };

  ngOnInit(): void {
    this.chartOptions = {
      series: [70],
      chart: {
        height: this.height,
        type: "radialBar"
      },
      plotOptions: {
        radialBar: {
          hollow: {
            size: "70%"
          }
        }
      },
      labels: ["Lessons"]
    };
  }

}
