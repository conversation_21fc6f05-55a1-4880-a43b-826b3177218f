
import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input, // Still keeping for now if there are plans to convert to signals later
  OnDestroy,
  OnInit,
  Output, // Still keeping for now if there are plans to convert to signals later
  SimpleChanges,
  ViewChild,
  inject,
  input,
  output,
  signal,
  computed
} from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import moment from 'moment-timezone';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import {
  AuthStateService,
  CalendarRoutes,
  CalendarService,
  HandleApiResponseService,
  IBasicProfileInfoDto,
  ICalendarEventDto,
  IGetCalendarDatesWithLessonsRequest,
  IGetCalendarDatesWithLessonsResponse,
  IGetStudentsLessonsCalendarResponse,
  IGetTeacherCalendarResponse,
  ILessonsScheduleDto,
  TimezoneService,
  UserRolesContext
} from 'SharedModules.Library';

/**
 * @interface LessonGroup
 * @description Represents a grouped structure of lessons by date and then by status,
 * counting the number of lessons for each status on a given date.
 */
interface LessonGroup {
  [date: string]: {
    [status: string]: {
      count: number;
    };
  };
}

/**
 * @interface LessonCounts
 * @description Represents a dictionary mapping lesson statuses to their respective counts for a specific day.
 */
interface LessonCounts {
  [status: string]: number;
}

/**
 * @interface CalendarDateWithLesson
 * @description Represents a date that has associated lessons, stored in a local date string format.
 */
interface CalendarDateWithLesson {
  localDate: string;
}

/**
 * @constant {string[]} WEEKDAYS
 * @description Array of abbreviated weekday names, starting from Monday.
 */
const WEEKDAYS: string[] = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];

/**
 * @constant {number} AGENDA_HEIGHT_CALC_DELAY
 * @description Delay in milliseconds before calculating agenda height to ensure DOM is ready.
 */
const AGENDA_HEIGHT_CALC_DELAY: number = 10;

@Component({
  selector: 'app-calendar-agenda',
  imports: [
    CommonModule,
    TooltipModule,
    ButtonModule,
  ],
  templateUrl: './calendar-agenda.component.html',
  styleUrl: './calendar-agenda.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CalendarAgendaComponent implements OnInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);
  @ViewChild('daysTableWrapper', { static: true }) public daysTableWrapper: ElementRef | undefined;

  // Input Signals
  enableButtons = input<boolean>(true);
  reverseGradient = input<boolean>(true);
  title = input<string>('Agenda');
  /**
   * @description Array of lesson objects. This input will be used reactively.
   */
  calHeaderClass = input<string>(' flex justify-content-between align-items-center');
  /**
   * @description The currently selected day provided as an input. Renamed to avoid conflict with signal.
   */
  currentSelectedDayInput = input<Date | string | null>(null);
  userIdsContext = input<UserRolesContext>({} as UserRolesContext);

  // Output Event Emitters
  @Output() dayClicked = new EventEmitter<Date>();
  @Output() agendaHeight = new EventEmitter<number>();
  @Output() currentMonthChanged = new EventEmitter<number>();
  /**
   * @description Emits an array of calendar events for the selected day/month.
   */
  calendarLessons = output<ICalendarEventDto[]>();

  // Injected Services
  private timezoneService = inject(TimezoneService);
  private authService = inject(AuthStateService);
  private handleApiService = inject(HandleApiResponseService);
  private readonly calendarService = inject(CalendarService);


  // Component State Signals
  readonly lessonDates = signal<{ localDate: string }[]>([]);
  weekdays: string[] = WEEKDAYS; // Using the constant
  currentDate = signal<Date>(new Date());
  /**
   * @description Stores the selected day for each month, keyed by 'YYYY-MM' format.
   */
  selectedDay = signal<number | undefined>(undefined);

  user = signal<IBasicProfileInfoDto | null>(null);
  loaded = signal<boolean>(false);
  userTimezone = signal<string>('');
  userBasicInfo = this.authService.getUserBasicInfo() as IBasicProfileInfoDto;
  isLoading = signal<boolean>(false);

  constructor(
    private cdr: ChangeDetectorRef,
    private elementRef: ElementRef,
  ) { }

  ngOnInit(): void {
    this.initializeUserData();
    this.initializeCurrentDate();
    this.setInitialLoadState();
    this.fetchMonthLessons();
  }

  /**
   * @description Lifecycle hook that reacts to changes in input properties.
   * Specifically handles changes to `lessons` and `currentSelectedDayInput`.
   * @param changes - An object containing the changed properties.
   */
  ngOnChanges(changes: SimpleChanges): void {

    if (changes['currentSelectedDayInput'] && changes['currentSelectedDayInput'].currentValue !== changes['currentSelectedDayInput'].previousValue) {
      this.initializeCurrentDate();
      this.fetchMonthLessons();
    }
  }

  ngAfterViewInit(): void {
    this.calculateAgendaHeight();
  }

  ngOnDestroy(): void {
    // HTTP calls and observable subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  /**
   * @description Initializes user data including basic info and timezone.
   * Falls back to default timezone if user's IANA timezone is not found.
   * @private
   */
  private initializeUserData(): void {
    this.user.set(this.authService.getUserClaims());
    if (this.userBasicInfo.timeZoneIana) {
      this.userTimezone.set(this.userBasicInfo.timeZoneIana);
      this.timezoneService.setTimezone(this.userTimezone());
    } else {
      this.userTimezone.set(this.timezoneService.getTimezone());
      console.warn('User IANA timezone not found, using default/guessed timezone:', this.userTimezone());
    }
  }

  /**
   * @description Sets the initial current date for the calendar.
   * Uses `currentSelectedDayInput` if provided, otherwise defaults to current time in user's timezone.
   * @private
   */
  private initializeCurrentDate(): void {
    const selectedDayValue = this.currentSelectedDayInput();
    if (selectedDayValue) {
      const initialDate = this.timezoneService.getDateInUserTimezone(new Date(selectedDayValue), this.userTimezone()).toDate();
      this.currentDate.set(initialDate);
      this.setSelectedDay(moment(initialDate).tz(this.userTimezone()).date()); // Set selected day
    } else {
      const todayInUserTz = this.timezoneService.getCurrentTimeInZone(this.userTimezone());
      this.currentDate.set(todayInUserTz);
      this.setSelectedDay(moment(todayInUserTz).tz(this.userTimezone()).date()); // Set today as selected
    }
  }

  /**
   * @description Sets the initial loading state based on `showAvailability` input.
   * @private
   */
  private setInitialLoadState(): void {

    this.loaded.set(true);
  }

  /**
   * @description Calculates and emits the height of the calendar agenda component.
   * Uses a setTimeout to ensure the DOM is rendered before calculating height.
   * @private
   */
  private calculateAgendaHeight(): void {
    setTimeout(() => {
      if (this.elementRef.nativeElement) {
        const elementHeight = this.elementRef.nativeElement.offsetHeight;
        this.agendaHeight.emit(elementHeight);
      }
    }, AGENDA_HEIGHT_CALC_DELAY);
  }

  /**
   * @description Fetches lesson dates for the current month based on the user's role context.
   * Handles loading states and processes the API response.
   * @private
   */
  private fetchMonthLessons(): void {
    this.isLoading.set(true);
    const monthRange = this.getCurrentMonthRange();

    /**
     * @type {IGetCalendarDatesWithLessonsRequest}
     * @description Request payload for fetching calendar dates with lessons.
     */
    const requestData: IGetCalendarDatesWithLessonsRequest = {
      startDateUtc: monthRange.startDateUTC as unknown as Date, // Type assertion due to shared DTO and moment's toISOString
      endDateUtc: monthRange.endDateUTC as unknown as Date,
      parentId: this.userIdsContext().parentId,
      studentId: this.userIdsContext().studentId,
      teacherId: this.userIdsContext().teacherId,
    };

    const currentMoment = this.timezoneService.getDateInUserTimezone(new Date(), this.userTimezone());
    const viewedMoment = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone());

    if (currentMoment.isSame(viewedMoment, 'month')) {
      requestData.currentDateStartUtc = monthRange.currentDateStartUtc as unknown as Date;
      requestData.currentDateEndUtc = monthRange.currentDateEndUtc as unknown as Date;
    }

    this.handleApiService.getApiData<IGetCalendarDatesWithLessonsResponse>(
      /**
       * @type {object}
       * @description API request configuration for fetching calendar dates with lessons.
       */
      {
        url: CalendarRoutes.getCalendarDatesWithLessons,
        method: 'GET',
      },
      requestData as IGetCalendarDatesWithLessonsRequest
    ).pipe(
      takeUntilDestroyed(this.destroyRef),
      catchError((error) => {
        console.error('Failed to fetch calendar dates with lessons:', error);
        this.lessonDates.set([]);
        this.isLoading.set(false);
        this.dayClicked.emit(undefined); // Emit undefined if an error occurs to reset state if needed
        return of(null); // Return null to indicate error state
      })
    ).subscribe({
      next: (response: IGetCalendarDatesWithLessonsResponse | null) => {
        if (!response) {
          this.isLoading.set(false);
          this.lessonDates.set([]);
          this.calendarLessons.emit([]); // Ensure calendarLessons is reset on no response
          return;
        }

        if (typeof response === 'object' && response.startTimeDatesInUtc && response.startTimeDatesInUtc.length > 0) {
          this.processLessonDates(response.startTimeDatesInUtc);

          const todayMoment = this.timezoneService.getDateInUserTimezone(new Date(), this.userTimezone());
          const viewedMonthMoment = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone());

          if (todayMoment.isSame(viewedMonthMoment, 'month')) {
            // If the currently viewed month is the actual current month (today's month)
            // then set the currentDate to today and select today's day.
            this.currentDate.set(todayMoment.toDate()); // Ensures currentDate is set to TODAY
            this.setSelectedDay(todayMoment.date()); // Select TODAY's day
            this.dayClicked.emit(todayMoment.toDate()); // Emit TODAY's date
          } else {
            // If viewing any other month (past or future), ensure no day is selected initially.
            this.setSelectedDay(undefined);
            this.dayClicked.emit(undefined); // Clear selected day in parent
          }
        } else {
          this.lessonDates.set([]);
        }
        this.isLoading.set(false);
      },
      // Error is now handled by catchError, so `error` in subscribe is less critical,
      // but good to keep for any unhandled errors downstream.
      error: (error) => {
        // This block will only be hit if catchError re-throws or if another error occurs after catchError.
        // For robustness, keeping it.
        console.error('An unhandled error occurred after catchError:', error);
        this.isLoading.set(false);
      }
    });
  }

  /**
   * @description Processes the UTC lesson dates received from the API into local dates
   * and updates the `lessonDates` signal.
   * @param utcDateInputs - Array of UTC date strings or Date objects.
   * @private
   */
  private processLessonDates(utcDateInputs: Array<Date | string>): void {
    const newLessonDates = utcDateInputs.map(utcDateInput => ({
      localDate: this.timezoneService.convertUtcToLocal(utcDateInput, {
        timezone: this.userTimezone(),
        outputFormat: 'string',
        formatPattern: 'YYYY-MM-DD'
      }),
    }));
    this.lessonDates.set(newLessonDates);
  }

  /**
   * @description Processes the calendar API response, merging lessons based on role
   * and filtering them to include only lessons from the current selected day onwards.
   * @param schedule - The API response containing schedule data.
   * @returns An array of merged and filtered calendar event DTOs.
   * @private
   */
  private processCalendarResponse(schedule: IGetStudentsLessonsCalendarResponse | IGetTeacherCalendarResponse): ICalendarEventDto[] {
    let mergedLessons: ICalendarEventDto[] = [];

    
    if ('scheduleDto' in schedule) {

      mergedLessons = this.calendarService.mergeLessons(schedule.scheduleDto as ILessonsScheduleDto);
    }
    if ('teacherScheduleDto' in schedule) {
      mergedLessons = this.calendarService.mergeLessons(schedule.teacherScheduleDto as ILessonsScheduleDto);
    } else if ('studentsLessonsScheduleDto' in schedule) {
      // Ensure studentsLessonsScheduleDto is treated as ILessonsScheduleDto
      mergedLessons = this.calendarService.mergeLessons(schedule.studentsLessonsScheduleDto as ILessonsScheduleDto);
    }

    console.log('Merged Lessons:', mergedLessons);
    // Filter lessons to only include those on or after the current selected day
    return mergedLessons.filter((lesson) => {
      const lessonMoment = moment.utc(lesson.startDateTime).tz(this.userTimezone());

      const currentSelectedDate = moment(this.currentDate()).tz(this.userTimezone());
      console.log('Lesson Date:', lessonMoment.format('YYYY-MM-DD'));
      console.log('Current Selected Date:', currentSelectedDate.format('YYYY-MM-DD'));
      console.log('Is After or Same:', lessonMoment.isSameOrAfter(currentSelectedDate, 'day'));
      console.log('-------------------------------------');
      return lessonMoment.isSameOrAfter(currentSelectedDate, 'day');
    });
  }

  /**
   * @description Retrieves the start and end UTC date ranges for the current month,
   * including the current day's UTC range.
   * @returns An object containing various date ranges in Date and ISO string formats.
   * @private
   * @throws {Error} If the user's timezone is invalid.
   */
  private getCurrentMonthRange(): {
    startDate: Date;
    endDate: Date;
    startDateUTC: string;
    endDateUTC: string;
    currentDateStartUtc: string;
    currentDateEndUtc: string;
  } {
    if (!this.timezoneService.isValidTimezone(this.userTimezone())) {
      throw new Error(`Invalid timezone: ${this.userTimezone()}`);
    }

    const startOfMonthLocal = this.timezoneService.getStartOf(this.currentDate(), 'month', this.userTimezone());
    const endOfMonthLocal = this.timezoneService.getEndOf(this.currentDate(), 'month', this.userTimezone());

    const todayLocal = this.timezoneService.getStartOf(new Date(), 'day', this.userTimezone());
    const todayEndLocal = this.timezoneService.getEndOf(new Date(), 'day', this.userTimezone());

    return {
      startDate: startOfMonthLocal,
      endDate: endOfMonthLocal,
      startDateUTC: this.timezoneService.convertLocalToUtc(startOfMonthLocal, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString(),
      endDateUTC: this.timezoneService.convertLocalToUtc(endOfMonthLocal, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString(),
      currentDateStartUtc: this.timezoneService.convertLocalToUtc(todayLocal, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString(),
      currentDateEndUtc: this.timezoneService.convertLocalToUtc(todayEndLocal, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString()
    };
  }

  /**
   * @description Checks and loads availability if `showAvailability` is true.
   * (Currently a placeholder for future logic).
   * @private
   */
  private checkAvailability(): void {

    this.loaded.set(true);
  }

  /**
   * @description Calculates the date range for a given local date, converting it to UTC.
   * @param date - The local date to calculate the range for.
   * @returns An object containing start and end dates in local and UTC formats.
   * @private
   * @throws {Error} If the user's timezone is invalid.
   */
  private getDateRangeForDay(date: Date): { startDate: string; endDate: string; startDateUTC: string; endDateUTC: string } {
    if (!this.timezoneService.isValidTimezone(this.userTimezone())) {
      throw new Error(`Invalid timezone: ${this.userTimezone()}`);
    }

    const startOfDayInUserTz = this.timezoneService.getStartOf(date, 'day', this.userTimezone());
    const endOfDayInUserTz = this.timezoneService.getEndOf(date, 'day', this.userTimezone());

    return {
      startDate: this.timezoneService.formatInTimezone(startOfDayInUserTz, 'YYYY-MM-DDTHH:mm:ssZ', this.userTimezone()),
      endDate: this.timezoneService.formatInTimezone(endOfDayInUserTz, 'YYYY-MM-DDTHH:mm:ssZ', this.userTimezone()),
      startDateUTC: this.timezoneService.convertLocalToUtc(startOfDayInUserTz, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString(),
      endDateUTC: this.timezoneService.convertLocalToUtc(endOfDayInUserTz, {
        sourceTimezone: this.userTimezone(), outputFormat: 'moment'
      }).toISOString()
    };
  }

  /**
   * @description Loads lessons for a specific date range based on the user's role context.
   * Updates the `calendarLessons` output emitter.
   * @param startDateUTC - The UTC start date for fetching lessons.
   * @param endDateUTC - The UTC end date for fetching lessons.
   * @private
   */
  private loadLessonsPerRole(startDateUTC: string, endDateUTC: string): void {
    this.isLoading.set(true);
    console.log('Requesting lessons for UTC range:', startDateUTC, 'to', endDateUTC);

    this.calendarService.getCalendarDataByRole(startDateUTC, endDateUTC, this.userIdsContext().studentId).pipe(
      takeUntilDestroyed(this.destroyRef),
      map((lessons: IGetStudentsLessonsCalendarResponse | IGetTeacherCalendarResponse) => {
        this.isLoading.set(false);
        return lessons;
      }),
      catchError((error) => {
        console.error('Error loading calendar data for day:', error);
        this.calendarLessons.emit([]);
        this.isLoading.set(false);
        return of(null); // Return observable of null to signify handled error and complete stream
      })
    ).subscribe({
      next: (schedule) => {
        if (schedule) { // Only process if schedule is not null (i.e., no error from catchError)

          const filteredLessons = this.processCalendarResponse(schedule);
          this.calendarLessons.emit(filteredLessons);
          console.log('Lessons loaded for day:', filteredLessons);
        }
      },
      error: (error) => {
        // This block will only be hit if catchError re-throws or if another error occurs after catchError.
        console.error('An unhandled error occurred during lesson loading:', error);
        this.isLoading.set(false);
      }
    });
  }

  // Public Methods (Ordered for better logical flow)

  /**
   * @description Gets the number of days in the current month.
   * @param date - The date to get the month from.
   * @returns The number of days in the month.
   */
  getDaysInMonth(date: Date): number {
    return this.timezoneService.getDateInUserTimezone(date, this.userTimezone()).daysInMonth();
  }

  /**
   * @description Generates a 2D array representing weeks and days in the current month for calendar display.
   * @param date - The date within the month to generate the grid for.
   * @returns A 2D array where each inner array represents a week, containing day objects.
   */
  getWeeksInMonth(date: Date): any[][] {
    const momentDateInUserTz = this.timezoneService.getDateInUserTimezone(date, this.userTimezone());
    const startOfMonth = momentDateInUserTz.clone().startOf('month');
    // Moment's isoWeekday() returns 1 for Monday, 7 for Sunday. Adjust to 0 for Monday.
    const correctedFirstDayOfMonth = (startOfMonth.isoWeekday() - 1 + 7) % 7;

    const daysInMonth = momentDateInUserTz.daysInMonth();
    const numWeeksInMonth = Math.ceil((correctedFirstDayOfMonth + daysInMonth) / 7);

    const weeks: any[][] = [];
    // Iterate through weeks
    for (let i = 0; i < numWeeksInMonth; i++) {
      const week: any[] = [];
      // Iterate through days of the week (0-6 for Mon-Sun)
      for (let j = 0; j < 7; j++) {
        // Calculate the day number in the grid (offset by the start day of the month)
        const dayNumberInGrid = (i * 7 + j) - correctedFirstDayOfMonth + 1; // +1 because day numbers start from 1

        const dayDateMoment = momentDateInUserTz.clone().startOf('month').date(dayNumberInGrid);

        const isPrevMonth = dayNumberInGrid < 1;
        const isNextMonth = dayNumberInGrid > daysInMonth;
        const isCurrentMonth = !isPrevMonth && !isNextMonth;

        week.push({
          number: dayDateMoment.date(), // Always show the actual day number for prev/next month as well
          name: this.weekdays[j],
          isPrevMonth,
          isNextMonth,
          isCurrentMonth,
          fullDate: dayDateMoment.toDate()
        });
      }
      weeks.push(week);
    }
    return weeks;
  }

  /**
   * @description Checks if a given day is today's date within the current month being viewed.
   * @param dayNumber - The day number (1-31).
   * @param isCurrentMonthDay - A boolean indicating if the day belongs to the current month.
   * @returns True if the day is today, false otherwise.
   */
  isTodayDate(dayNumber: number, isCurrentMonthDay: boolean): boolean {
    if (!isCurrentMonthDay) return false;
    const todayMoment = this.timezoneService.getDateInUserTimezone(null, this.userTimezone());
    const dayInViewedMonthMoment = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).date(dayNumber);
    return dayInViewedMonthMoment.isSame(todayMoment, 'day');
  }

  /**
   * @description Gets the formatted name of the current month.
   * Emits the month index via `currentMonthChanged` output.
   * @returns The full name of the current month (e.g., "January").
   */
  getCurrentMonth(): string {
    const currentMonthMoment = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone());
    this.currentMonthChanged.emit(currentMonthMoment.month());
    return currentMonthMoment.format('MMMM');
  }

  /**
   * @description Gets the year of the current month being viewed.
   * @returns The year.
   */
  getCurrentYear(): number {
    return this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).year();
  }

  /**
   * @description Navigates the calendar to the next month.
   * Resets `selectedDays` if the month changes.
   */
  goForwardMonth(): void {
    const previousMonth = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).month();
    this.currentDate.set(this.timezoneService.addDuration(this.currentDate(), 1, 'month', this.userTimezone()));
    const newMonth = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).month();

    if (previousMonth !== newMonth) {
      this.setSelectedDay(undefined); // Explicitly deselect the day
      this.dayClicked.emit(undefined); // Inform parent about deselection
    }
    this.checkAvailability();
    this.calculateAgendaHeight();
    this.fetchMonthLessons();
  }

  /**
   * @description Navigates the calendar to the previous month.
   * Resets `selectedDays` if the month changes.
   */
  goBackwardMonth(): void {
    const previousMonth = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).month();
    this.currentDate.set(this.timezoneService.subtractDuration(this.currentDate(), 1, 'month', this.userTimezone()));
    const newMonth = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).month();

    if (previousMonth !== newMonth) {
      this.setSelectedDay(undefined); // Explicitly deselect the day
      this.dayClicked.emit(undefined); // Inform parent about deselection
    }
    this.checkAvailability();
    this.calculateAgendaHeight();
    this.fetchMonthLessons();
  }

  /**
   * @description Sets the selected day for the current month.
   * @param day - The day number to select.
   */
  setSelectedDay(day: number | undefined): void {
    this.selectedDay.set(day);
  }

  /**
   * @description Handles the click event on a day cell.
   * Loads lessons for the clicked day and updates the `currentDate` and `dayClicked` output.
   * @param dayCell - The day cell object from the calendar grid.
   * @param checkCurrentMonth - Optional. If true, only handles clicks for days in the current month.
   */
  onDayClicked(dayCell: any, checkCurrentMonth: boolean = false): void {
    if (checkCurrentMonth && !dayCell.isCurrentMonth) {
      return;
    }

    const clickedDate = dayCell.fullDate;
    const dateRange = this.getDateRangeForDay(clickedDate);

    this.loadLessonsPerRole(dateRange.startDateUTC, dateRange.endDateUTC);
    this.currentDate.set(clickedDate); // Update current date signal
    this.dayClicked.emit(clickedDate);
    this.setSelectedDay(moment(clickedDate).tz(this.userTimezone()).date());
  }

  /**
   * @description Checks if a given day is in the past relative to today.
   * @param dayNumber - The day number (1-31).
   * @param isCurrentMonthDay - A boolean indicating if the day belongs to the current month.
   * @returns True if the day is in the past, false otherwise.
   */
  isDayInThePast(dayNumber: number, isCurrentMonthDay: boolean): boolean {
    if (!isCurrentMonthDay) return false;

    const todayStartOfDay = this.timezoneService.getToday();

    const dateToCheck = this.timezoneService.getStartOf(
      this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone()).date(dayNumber).toDate(),
      'day',
      this.userTimezone()
    );
    return dateToCheck < todayStartOfDay;
  }


  /**
   * @description Converts a lesson status object into an array of objects
   * with `status` (lowercase and hyphenated) and `count` properties.
   * @param status - The lesson status object.
   * @returns An array of lesson status objects.
   */
  getStatusArray(status: LessonCounts): Array<{ status: string; count: number }> {
    return Object.entries(status).map(([key, value]) => ({
      status: key.toLowerCase().replace(/\s+/g, '-'),
      count: value as number
    }));
  }

  /**
   * @description Checks if a specific day has any lessons.
   * @param dayNumber - The day number to check.
   * @param isCurrentMonthDay - A boolean indicating if the day belongs to the current month.
   * @returns True if there are lessons on the day, false otherwise.
   */
  hasLessonsOnDay(dayNumber: number, isCurrentMonthDay: boolean): boolean {
    if (!isCurrentMonthDay || !this.lessonDates().length) return false;

    const dateToCheckFormatted = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone())
      .date(dayNumber)
      .format('YYYY-MM-DD');

    return this.lessonDates().some(lessonDate => lessonDate.localDate === dateToCheckFormatted);
  }

  /**
   * @description Retrieves the lesson dates for a specific day.
   * @param dayNumber - The day number to get lessons for.
   * @returns An array of `CalendarDateWithLesson` objects for the specified day.
   */
  getLessonsForDay(dayNumber: number): CalendarDateWithLesson[] {
    if (!this.lessonDates().length) return [];

    const dateToCheckFormatted = this.timezoneService.getDateInUserTimezone(this.currentDate(), this.userTimezone())
      .date(dayNumber)
      .format('YYYY-MM-DD');

    return this.lessonDates().filter(lessonDate => lessonDate.localDate === dateToCheckFormatted);
  }

  /**
   * @description Gets the total count of lessons for a specific day.
   * @param dayNumber - The day number to get the lesson count for.
   * @param isCurrentMonthDay - A boolean indicating if the day belongs to the current month.
   * @returns The number of lessons on the day.
   */
  getLessonCountForDay(dayNumber: number, isCurrentMonthDay: boolean): number {
    if (!isCurrentMonthDay) return 0;
    return this.getLessonsForDay(dayNumber).length;
  }
}
