import {CommonModule} from '@angular/common';
import {ChangeDetectionStrategy, Component, Input, type OnInit, ViewChild} from '@angular/core';
import {ChartComponent, NgApexchartsModule} from 'ng-apexcharts';
import {ChartOptions} from '../radial-bar-chart/radial-bar-chart.component';

@Component({
    selector: 'app-area-chart',
    imports: [
        CommonModule,
        NgApexchartsModule,
    ],
    templateUrl: './area-chart.component.html',
    styleUrl: './area-chart.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AreaChartComponent implements OnInit {
  @Input() height: number = 280;
  @ViewChild("chart") chart: ChartComponent = {} as ChartComponent;
  public chartOptions: Partial<ChartOptions> = {};

  ngOnInit(): void {
    this.chartOptions = {
      series: [
        {
          name: "Reading",
          data: this.generateDayWiseTimeSeries(
            new Date("11 Feb 2017 GMT").getTime(),
            20,
            {
              data: [31, 40, 28, 51, 42, 109, 100]

            }
          )
        },
        {
          name: "Writing",
          data: this.generateDayWiseTimeSeries(
            new Date("11 Feb 2017 GMT").getTime(),
            20,
            {
              data: [11, 32, 45, 32, 34, 52, 41]

            }
          )
        },
        {
          name: "Speaking",
          data: this.generateDayWiseTimeSeries(
            new Date("11 Feb 2017 GMT").getTime(),
            20,
            {
              data: [15, 11, 32, 18, 9, 24, 11]

            }
          )
        },
        {
          name: "Listening",
          data: this.generateDayWiseTimeSeries(
            new Date("11 Feb 2017 GMT").getTime(),
            20,
            {
              data: [11, 32, 45, 32, 34, 52, 41]

            }
          )
        },
        {
          name: "Attending",
          data: this.generateDayWiseTimeSeries(
            new Date("11 Feb 2017 GMT").getTime(),
            20,
            {
              data: [31, 40, 28, 51, 42, 109, 100]

            }
          )
        }
      ],
      colors: ['#9B96E7', '#FC998A', '#9B78B4', '#48CDDF', '#8ACCF7', '#9B78B4', '#ff7c43', '#ffa600'],
      chart: {
        height: 350,
        type: "area",
        stacked: true,
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        colors: ['#9B96E7', '#FC998A', '#9B78B4', '#48CDDF', '#8ACCF7', '#9B78B4', '#ff7c43', '#ffa600'],
        curve: 'monotoneCubic'
      },
      fill: {
        colors: ['#9B96E7', '#FC998A', '#9B78B4', '#4CBFF5', '#8ACCF7', '#9B78B4', '#ff7c43', '#ffa600'],
        type: 'gradient',
        gradient: {
          opacityFrom: 0.6,
          opacityTo: 0.8,
        }
      },
      legends: {
        markers: {}
      },
      xaxis: {
        type: "datetime",
        labels: {
          formatter: (value: number) => {
            const date = new Date(value);
            return `${date.toLocaleDateString()}`;
          }
        }
      },
      tooltip: {
        markers: {}
      },
      labels: {
        enabled: false,
        show: false
      }
    };

    console.log(this.chartOptions.series);
  }

  public generateDayWiseTimeSeries = function (baseval: number, count: number, yrange: { data: number[] }) {
    var i = 0;
    var series = [];
    while (i < count) {
      var x = baseval;
      var y = yrange.data[i % yrange.data.length];

      series.push([x, y]);
      baseval += 86400000;
      i++;
    }
    return series
  };
}
