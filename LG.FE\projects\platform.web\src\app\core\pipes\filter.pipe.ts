import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter',
  standalone: true
})
export class FilterPipe implements PipeTransform {

  transform(data: unknown[], filterProperty: string, filter: string): unknown[] {
    console.log(filterProperty);
    if (!filter) {
      return data;
    }
    console.log(filter);
    const filterValue = filter.toLowerCase();
    return filterValue
      ? data.filter(item => item[filterProperty].toLowerCase().includes(filterValue))
      : data;
  }

}