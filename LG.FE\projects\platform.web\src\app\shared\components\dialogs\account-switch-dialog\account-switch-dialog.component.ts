import { CommonModule } from '@angular/common';
import { Component, DestroyRef, EventEmitter, inject, Inject, Input, Output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { AuthStateService, CustomDialogPopupComponent, EmitEvent, EventBusService, Events, HandleApiResponseService, ToastService } from 'SharedModules.Library';

export interface StudentSwitchData {
  userId: string;
  firstName: string;
  lastName: string;
}

export interface AccountSwitchResult {
  confirmed: boolean;
  studentData?: StudentSwitchData;
}

@Component({
  imports: [CommonModule, ButtonModule, DialogModule, CustomDialogPopupComponent],
  selector: 'app-account-switch-dialog',
  templateUrl: './account-switch-dialog.component.html',
  styleUrls: ['./account-switch-dialog.component.scss']
})
export class AccountSwitchDialogComponent {
  @Input() visible: boolean = false;
  @Input() studentData!: StudentSwitchData;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() switchConfirmed = new EventEmitter<AccountSwitchResult>();
  @Output() switchCancelled = new EventEmitter<void>();
  eventBusService = inject(EventBusService);
  toastService = inject(ToastService);
  apiService = inject(HandleApiResponseService);
  authService = inject(AuthStateService);
  private readonly destroy: DestroyRef = inject(DestroyRef);
  parameters: any;

  // Galaxy transition state
  isTransitioning: boolean = false;


  constructor(@Inject('dialogParameters') parameters: any) {
    this.studentData = (parameters);
    console.log(this.studentData);
    this.visible = true;
  }

  onDialogHide() {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  onCancel() {
    this.visible = false;
    this.visibleChange.emit(false);
    this.switchCancelled.emit();
  }

  onConfirm() {
    // Start galaxy transition animation
    this.isTransitioning = true;

    // Delay the actual account switch to allow animation to complete
    setTimeout(() => {
      this.visible = false;
      this.visibleChange.emit(false);
      this.eventBusService.emit(new EmitEvent(Events.StateLoadStartImpersonate,
        {
          impersonateStudentId: this.studentData.userId,
          parentRefreshToken: this.authService.getRefreshToken()
        }
      ));
    }, 1200); // 1.2 second delay for animation
  }
}
