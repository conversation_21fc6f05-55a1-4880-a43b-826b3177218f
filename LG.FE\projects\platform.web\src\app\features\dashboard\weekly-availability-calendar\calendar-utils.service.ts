import { Injectable } from '@angular/core';
import {
    ILessonStatusEnum,
    IPackageTypeEnum,
    ILanguageLevelsEnum,
    ICalendarLessonParticipantDto,
} from 'SharedModules.Library';

// Strongly typed utility service for calendar operations
@Injectable({
    providedIn: 'root'
})
export class CalendarUtilsService {

    // Strongly typed status display labels
    private readonly statusDisplayLabels: Record<ILessonStatusEnum, string> = {
        [ILessonStatusEnum.Scheduled]: 'Scheduled',
        [ILessonStatusEnum.AwaitingEvaluation]: 'Awaiting Evaluation',
        [ILessonStatusEnum.Completed]: 'Completed',
        [ILessonStatusEnum.StudentNoShow]: 'No Show',
        [ILessonStatusEnum.TeacherNoShow]: 'No Show',
        [ILessonStatusEnum.CancelledByParent]: 'Canceled',
        [ILessonStatusEnum.CancelledByTeacher]: 'Canceled',
        [ILessonStatusEnum.PendingConfirmationByTeacher]: 'Pending Confirmation',
    } as const;

    // Strongly typed package type labels
    private readonly packageTypeLabels: Record<IPackageTypeEnum, string> = {
        [IPackageTypeEnum.FreeTrial]: 'Free Trial Lesson',
        [IPackageTypeEnum.Gift]: 'Gift Lesson',
        [IPackageTypeEnum.Paid]: 'Paid Lesson',
    } as const;

    // Strongly typed package badge classes
    private readonly packageBadgeClasses: Record<IPackageTypeEnum, string> = {
        [IPackageTypeEnum.FreeTrial]: 'trial-badge',
        [IPackageTypeEnum.Gift]: 'gift-badge',
        [IPackageTypeEnum.Paid]: 'paid-badge',
    } as const;

    // Short package type labels for display
    private readonly shortPackageLabels: Record<IPackageTypeEnum, string> = {
        [IPackageTypeEnum.FreeTrial]: 'Trial',
        [IPackageTypeEnum.Gift]: 'Gift',
        [IPackageTypeEnum.Paid]: 'Paid',
    } as const;

    /**
     * Get display label for lesson status
     */
    getStatusDisplayLabel(status: ILessonStatusEnum): string {
        return this.statusDisplayLabels[status] || 'Unknown';
    }

    /**
     * Get full package type label
     */
    getPackageTypeLabel(packageType: IPackageTypeEnum): string {
        return this.packageTypeLabels[packageType] || '';
    }

    /**
     * Get short package type label for compact display
     */
    getShortPackageLabel(packageType: IPackageTypeEnum): string {
        return this.shortPackageLabels[packageType] || '';
    }

    /**
     * Get CSS class for package badge
     */
    getPackageBadgeClass(packageType: IPackageTypeEnum): string {
        return this.packageBadgeClasses[packageType] || '';
    }

    /**
     * Format lesson status from string/number to display text
     */
    formatLessonStatus(status: string | number): string {
        if (!status && status !== 0) return '';
        
        const statusEnum = typeof status === 'string' ? Number(status) : status;
        return this.statusDisplayLabels[statusEnum as ILessonStatusEnum] || String(status);
    }

    /**
     * Get enum key from enum value
     */
    getLessonStatusEnumKey(statusValue: ILessonStatusEnum): string {
        return Object.keys(ILessonStatusEnum).find(
            key => isNaN(Number(key)) && 
                   ILessonStatusEnum[key as keyof typeof ILessonStatusEnum] === statusValue
        ) || String(statusValue);
    }

    /**
     * Get package type enum key from enum value
     */
    getPackageTypeEnumKey(packageValue: IPackageTypeEnum): string {
        return Object.keys(IPackageTypeEnum).find(
            key => isNaN(Number(key)) && 
                   IPackageTypeEnum[key as keyof typeof IPackageTypeEnum] === packageValue
        ) || String(packageValue);
    }

    /**
     * Format language level for display
     */
    formatLanguageLevel(level: keyof typeof ILanguageLevelsEnum | string | ILanguageLevelsEnum): string {
        if (typeof level === 'number') {
            // If it's a numeric enum value, find the key
            const enumKey = Object.keys(ILanguageLevelsEnum).find(
                key => isNaN(Number(key)) && 
                       ILanguageLevelsEnum[key as keyof typeof ILanguageLevelsEnum] === level
            );
            return enumKey || String(level);
        }
        
        if (typeof level === 'string' && level in ILanguageLevelsEnum) {
            const enumValue = ILanguageLevelsEnum[level as keyof typeof ILanguageLevelsEnum];
            return typeof enumValue === 'string' ? enumValue : String(enumValue);
        }
        
        return String(level) || '';
    }

    /**
     * Format students list for display
     */
    formatStudentsList(students: ICalendarLessonParticipantDto[]): string {
        if (!students?.length) return '';
        
        return students.length === 1
            ? `<span class="student-name">${students[0].firstName} ${students[0].lastName}</span>`
            : `<span class="student-count">${students.length} students</span>`;
    }

    /**
     * Sanitize HTML content
     */
    sanitizeHtml(value: string | undefined | null): string {
        if (!value) return '';
        
        return String(value)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * Create lesson status options for dropdowns
     */
    createLessonStatusOptions(): Array<{
        label: string;
        value: ILessonStatusEnum;
        enumKey: string;
    }> {
        return Object.keys(ILessonStatusEnum)
            .filter(key => isNaN(Number(key)))
            .map(key => ({
                label: this.statusDisplayLabels[
                    ILessonStatusEnum[key as keyof typeof ILessonStatusEnum]
                ] || key,
                value: ILessonStatusEnum[key as keyof typeof ILessonStatusEnum],
                enumKey: key,
            }));
    }

    /**
     * Create package type options for dropdowns
     */
    createPackageTypeOptions(): Array<{
        label: string;
        value: IPackageTypeEnum;
        enumKey: string;
    }> {
        return Object.keys(IPackageTypeEnum)
            .filter(key => isNaN(Number(key)))
            .map(key => ({
                label: this.packageTypeLabels[
                    IPackageTypeEnum[key as keyof typeof IPackageTypeEnum]
                ] || key,
                value: IPackageTypeEnum[key as keyof typeof IPackageTypeEnum],
                enumKey: key,
            }));
    }
}
