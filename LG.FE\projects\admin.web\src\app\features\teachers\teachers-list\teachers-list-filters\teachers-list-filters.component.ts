import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  signal,
  computed,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  DestroyRef,
  inject
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { SliderModule } from 'primeng/slider';
import { DatePickerModule } from 'primeng/datepicker';
import {
  IGetTeachersRequest,
  ITeachingLanguageDto,
  IAvailabilityStatusOptionsEnum,
  ITeacherStudentAgesExperienceEnum,
  ITeacherStudentAgesPreferenceEnum,
  nameOf,
  EnumDropdownOptionsService,
  GeneralService
} from 'SharedModules.Library';

/**
 * Interface for filter change events
 */
export interface ITeachersFilterChangeEvent {
  filterName: keyof IGetTeachersRequest;
  value: any;
  resetPage?: boolean;
}

/**
 * Interface for filter action events
 */
export interface ITeachersFilterActionEvent {
  action: 'search' | 'reset';
  filters: IGetTeachersRequest;
}

/**
 * Interface for filter state data passed from parent
 */
export interface ITeachersFilterState {
  queryParams: IGetTeachersRequest;
  studentAgesRange: number[];
  selectedAvailabilityStatuses: IAvailabilityStatusOptionsEnum[];
  selectedTeachingAgesExperience: ITeacherStudentAgesExperienceEnum[];
  selectedTeacherStudentAgesPreference: ITeacherStudentAgesPreferenceEnum[];
  teachingLanguages: ITeachingLanguageDto[];
  nativeLanguages: string[];
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface ITeachersFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

@Component({
  selector: 'app-teachers-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DropdownModule,
    MultiSelectModule,
    CheckboxModule,
    ButtonModule,
    SliderModule,
    DatePickerModule
  ],
  templateUrl: './teachers-list-filters.component.html',
  styleUrl: './teachers-list-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeachersListFiltersComponent implements OnInit, OnChanges {
  private destroyRef = inject(DestroyRef);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private generalService = inject(GeneralService);

  /**
   * Filter state data from parent component
   */
  @Input() filterState: ITeachersFilterState = {
    queryParams: {} as IGetTeachersRequest,
    studentAgesRange: [2, 17],
    selectedAvailabilityStatuses: [],
    selectedTeachingAgesExperience: [],
    selectedTeacherStudentAgesPreference: [],
    teachingLanguages: [],
    nativeLanguages: [],
    isFilterOpen: true
  };

  /**
   * Filter configuration
   */
  @Input() config: ITeachersFilterConfig = {
    showToggleButton: true,
    defaultOpen: true,
    enableAutoSearch: false,
    searchDebounceMs: 400
  };

  /**
   * Emitted when a filter value changes
   */
  @Output() filterChanged = new EventEmitter<ITeachersFilterChangeEvent>();

  /**
   * Emitted when filter actions are triggered (search/reset)
   */
  @Output() filterAction = new EventEmitter<ITeachersFilterActionEvent>();

  /**
   * Emitted when filter panel is toggled
   */
  @Output() filterToggled = new EventEmitter<boolean>();

  // Internal signals for reactive state management
  private _isFilterOpen = signal<boolean>(true);
  private _studentAgesRange = signal<number[]>([2, 17]);
  private _selectedAvailabilityStatuses = signal<IAvailabilityStatusOptionsEnum[]>([]);
  private _selectedTeachingAgesExperience = signal<ITeacherStudentAgesExperienceEnum[]>([]);
  private _selectedTeacherStudentAgesPreference = signal<ITeacherStudentAgesPreferenceEnum[]>([]);

  // Computed properties for reactive access
  readonly isFilterOpen = computed(() => this._isFilterOpen());
  readonly studentAgesRange = computed(() => this._studentAgesRange());
  readonly selectedAvailabilityStatuses = computed(() => this._selectedAvailabilityStatuses());
  readonly selectedTeachingAgesExperience = computed(() => this._selectedTeachingAgesExperience());
  readonly selectedTeacherStudentAgesPreference = computed(() => this._selectedTeacherStudentAgesPreference());

  // Field names for type safety
  readonly getTeachersRequestFieldNames = nameOf<IGetTeachersRequest>();

  ngOnInit(): void {
    console.debug('Teachers List Filters Component initialized');

    // Initialize internal state with input state
    this.syncInternalState();
  }

  ngOnChanges(): void {
    // Sync internal state when input changes
    this.syncInternalState();
  }

  private syncInternalState(): void {
    this._isFilterOpen.set(this.filterState.isFilterOpen);
    this._studentAgesRange.set(this.filterState.studentAgesRange);
    this._selectedAvailabilityStatuses.set(this.filterState.selectedAvailabilityStatuses);
    this._selectedTeachingAgesExperience.set(this.filterState.selectedTeachingAgesExperience);
    this._selectedTeacherStudentAgesPreference.set(this.filterState.selectedTeacherStudentAgesPreference);
  }



  /**
   * Handles filter value changes
   */
  onFilterChange(filterName: keyof IGetTeachersRequest, value: any, resetPage = true): void {
    this.filterChanged.emit({
      filterName,
      value,
      resetPage
    });
  }

  /**
   * Handles multi-select changes for availability statuses
   */
  onAvailabilityStatusChange(value: IAvailabilityStatusOptionsEnum[]): void {
    this._selectedAvailabilityStatuses.set(value);
    // Don't emit immediately - wait for search action
  }

  /**
   * Handles multi-select changes for teaching ages experience
   */
  onTeachingAgesExperienceChange(value: ITeacherStudentAgesExperienceEnum[]): void {
    this._selectedTeachingAgesExperience.set(value);
    // Don't emit immediately - wait for search action
  }

  /**
   * Handles multi-select changes for student ages preference
   */
  onStudentAgesPreferenceChange(value: ITeacherStudentAgesPreferenceEnum[]): void {
    this._selectedTeacherStudentAgesPreference.set(value);
    // Don't emit immediately - wait for search action
  }

  /**
   * Handles student ages range changes
   */
  onStudentAgesRangeChange(value: number[]): void {
    this._studentAgesRange.set(value);
    this.onFilterChange('studentAgesMin', value[0]);
    this.onFilterChange('studentAgesMax', value[1]);
  }

  /**
   * Gets the current filter state for external use (e.g., by drawer component)
   */
  getCurrentFilters(): IGetTeachersRequest {
    const currentState = this.filterState;
    return {
      ...currentState.queryParams,
      availabilityStatus: this.generalService.convertArrayToFlags(this._selectedAvailabilityStatuses()),
      teachingAgesExperience: this.generalService.convertArrayToFlags(this._selectedTeachingAgesExperience()),
      teacherStudentAgesPreference: this.generalService.convertArrayToFlags(this._selectedTeacherStudentAgesPreference()),
      studentAgesMin: this._studentAgesRange()[0],
      studentAgesMax: this._studentAgesRange()[1],
      pageNumber: 1 // Reset to first page on search
    };
  }

  /**
   * Resets all filter values to defaults
   */
  resetFilters(): void {
    // Reset internal state
    this._selectedAvailabilityStatuses.set([]);
    this._selectedTeachingAgesExperience.set([]);
    this._selectedTeacherStudentAgesPreference.set([]);
    this._studentAgesRange.set([2, 17]);
  }

  /**
   * Emits search action with current filter state
   */
  emitSearchAction(): void {
    this.filterAction.emit({
      action: 'search',
      filters: this.getCurrentFilters()
    });
  }

  /**
   * Emits reset action
   */
  emitResetAction(): void {
    this.resetFilters();
    this.filterAction.emit({
      action: 'reset',
      filters: {} as IGetTeachersRequest // Parent will handle creating default request
    });
  }


}
