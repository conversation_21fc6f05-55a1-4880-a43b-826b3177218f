import { ViewportScroller } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { FormGroup, FormArray, AbstractControl } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class FormErrorScrollerService {

  viewportScroller = inject(ViewportScroller);
  /**
   * Validates the form, marks all fields as touched, and scrolls to the first error.
   * @param form The FormGroup to validate.
   * @returns Boolean indicating if the form is valid.
   */
  validateAndScrollToFirstError(form: FormGroup): boolean {
    if (!form) return false;

    this.markAllAsTouched(form);
    const firstInvalidControl = this.findFirstInvalidControl(form);
    console.log("🚀 ~ FormErrorScrollerService ~ validateAndScrollToFirstError ~ firstInvalidControl:", firstInvalidControl)


    if (firstInvalidControl) {
      this.scrollToFirstInvalidControl();
    }

    return form.valid;
  }

  /**
   * Marks all form controls as touched to trigger validation.
   * @param form The FormGroup or FormArray to mark as touched.
   */
  private markAllAsTouched(form: FormGroup | FormArray): void {
    Object.keys(form.controls).forEach(key => {
      const control = form.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markAllAsTouched(control);
      } else {
        control?.markAsTouched();
        control?.updateValueAndValidity();
      }
    });
  }

  /**
   * Finds the first invalid control in the form.
   * @param form The FormGroup to search.
   * @returns The first invalid AbstractControl or null.
   */
  private findFirstInvalidControl(form: FormGroup | FormArray): AbstractControl | null {
    for (const key of Object.keys(form.controls)) {
      const control = form.get(key);

      if (control && control.invalid) {
        if (control instanceof FormGroup || control instanceof FormArray) {
          const nestedInvalid = this.findFirstInvalidControl(control);
          return nestedInvalid || control;
        }
        return control;
      }
    }
    return null;
  }

  /**
   * Scrolls to the first invalid control in the DOM.
   * @param control The invalid form control.
   */
  private scrollToFirstInvalidControl(): void {
    setTimeout(() => {
      // Find the first invalid input, textarea, or select
      const firstInvalidElement = document.querySelector('input.ng-invalid, textarea.ng-invalid, select.ng-invalid');

      if (firstInvalidElement) {
        const yOffset = -120; // Adjust for sticky headers

        // Get element position with offset
        const y = firstInvalidElement.getBoundingClientRect().top + window.scrollY + yOffset;

        // Smooth scrolling using window.scrollTo
        window.scrollTo({ top: y, behavior: 'smooth' });

        // Focus on the field after scrolling
        setTimeout(() => {
          (firstInvalidElement as HTMLElement).focus();
        }, 500); // Delay to allow scrolling to complete
      }
    }, 30);
  }


  /**
   * Finds the corresponding DOM element for an invalid control.
   * @param control The AbstractControl to find.
   * @param formElement The HTML form element.
   * @returns The corresponding HTMLElement or null.
   */
  private findControlElement(control: AbstractControl, formElement: HTMLElement): HTMLElement | null {
    const controlName = this.getControlName(control);
    if (!controlName) return null;

    // Broaden search scope to handle reusable components
    const controlElement = document.querySelector(
      `[formControlName="${controlName}"], [name="${controlName}"], [id="${controlName}"], .ng-invalid`
    );

    if (!controlElement) {
      console.warn(`Control element not found for control name: ${controlName}`);
    }

    return controlElement as HTMLElement | null;
  }




  /**
   * Gets the name of a form control by searching through the form.
   * @param control The control to find the name for.
   * @returns The name of the control as a string.
   */
  private getControlName(control: AbstractControl): string | null {
    let controlName: string | null = null;

    // Check for the control in the form
    document.querySelectorAll('[formControlName], [name], [id]').forEach((element: Element) => {
      if (element.control === control) {
        controlName = element.getAttribute('formControlName') || element.getAttribute('name') || element.getAttribute('id');
      }
    });

    if (!controlName) {
      console.warn('Control name not found for the provided control:', control);
    }

    return controlName;
  }


}
