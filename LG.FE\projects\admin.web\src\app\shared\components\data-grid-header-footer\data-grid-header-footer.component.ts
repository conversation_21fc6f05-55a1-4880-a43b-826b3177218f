import { CommonModule } from '@angular/common';
import { Component, Input, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { TableModule } from 'primeng/table';
import { IDataGridFields } from 'SharedModules.Library';

@Component({
  selector: 'app-data-grid-header-footer',
  standalone: true,
  imports: [
    CommonModule,
    TableModule
  ],
  templateUrl: './data-grid-header-footer.component.html',
  styleUrls: ['./data-grid-header-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataGridHeaderFooterComponent {
  /**
   * Array of column definitions for the data grid
   */
  @Input() columns: IDataGridFields[] = [];

  /**
   * Toggle header visibility
   */
  @Input() showHeader: boolean = true;

  /**
   * Toggle footer visibility
   */
  @Input() showFooter: boolean = true;

  /**
   * Enable/disable sorting functionality
   */
  @Input() sortable: boolean = true;

  /**
   * Show actions column (frozen left column)
   */
  @Input() showActionsColumn: boolean = true;

  /**
   * Actions column header text
   */
  @Input() actionsColumnHeader: string = 'Actions';

  /**
   * Actions column width
   */
  @Input() actionsColumnWidth: string = '100px';

  /**
   * Enable column reordering
   */
  @Input() reorderable: boolean = true;

  /**
   * Custom CSS classes to apply to header/footer rows
   */
  @Input() customRowClass: string = '';

  /**
   * Get the style string for a column based on its maxWidth property
   */
  getColumnStyle(column: IDataGridFields): string {
    if (column.maxWidth !== undefined) {
      return `max-width:${column.maxWidth} !important; width:max-content !important;`;
    }
    return 'width:max-content !important;';
  }

  /**
   * Check if a column is sortable
   */
  isColumnSortable(column: IDataGridFields): boolean {
    return this.sortable && column.sortable === true;
  }

  /**
   * Check if column reordering is enabled
   */
  isColumnReorderable(): boolean {
    return this.reorderable;
  }
}
