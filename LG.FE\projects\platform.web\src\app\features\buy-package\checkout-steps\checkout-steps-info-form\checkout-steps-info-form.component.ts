import {
  CustomValidators,
  FormErrorScrollerService,
  IBasket,
  ICountry,
  IDialCodeDataDto,
  IGetBasketResponse,
  IGetProfileInfoRequest,
  IGetProfileInfoResponse,
  IProfileInfo,
  IWhereDidYouHearAboutUsDto,
  PrimeReactiveFormInputComponent
} from 'SharedModules.Library';
import { CommonModule, ViewportScroller } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  EventEmitter,
  inject,
  OnDestroy,
  OnInit,
  Output,
  signal,
  WritableSignal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { untilDestroyed } from 'SharedModules.Library';
import { Severity } from 'SharedModules.Library';
import { HandleApiResponseService } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { CheckoutService } from '@platform.src/app/core/services/checkout.service';
import { DataApiStateService, State } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { CountryPhoneInputComponent } from 'SharedModules.Library';
import { FormFieldValidationMessageComponent } from 'SharedModules.Library';
import { PrimeCountriesDropdownComponent } from 'SharedModules.Library';
import { ToastService } from 'SharedModules.Library';
import { SkeletonLoaderComponent } from 'SharedModules.Library';
import { FieldsetModule } from 'primeng/fieldset';

@Component({
  selector: 'app-checkout-steps-info-form',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    DropdownModule,
    ButtonModule,
    RadioButtonModule,
    CheckboxModule,
    FieldsetModule,
    PrimeReactiveFormInputComponent,
    CountryPhoneInputComponent,
    PrimeCountriesDropdownComponent,
    FormFieldValidationMessageComponent,
    SkeletonLoaderComponent,
  ],
  templateUrl: './checkout-steps-info-form.component.html',
  styleUrl: './checkout-steps-info-form.component.scss',
})
export class CheckoutStepsInfoFormComponent implements OnInit, OnDestroy {
  generalService = inject(GeneralService);
  checkoutService = inject(CheckoutService);
  apiService = inject(HandleApiResponseService);
  authService = inject(AuthStateService);
  dataStateService = inject(DataApiStateService);
  toastService = inject(ToastService);
  viewportScroller = inject(ViewportScroller);
  formErrorScroller = inject(FormErrorScrollerService);
  @Output() formSubmit = new EventEmitter<FormGroup>();
  userForm: FormGroup = new FormGroup({});
  countriesData: Record<string, unknown> = {};

  user = computed(() => this.authService.getUserClaims());
  userProfileInfo = computed(() => this.authService.getUserProfileInfo());

  formLoading = signal<boolean>(false);
  showSummary = signal(false);
  submitButton = signal<boolean>(true);
  referralOptions = signal<IWhereDidYouHearAboutUsDto[]>([]);
  countryCode = signal<string>('');
  buttonDisabled = signal<boolean>(false);
  controlNames: Record<string, string> = {};
  isBillingSameAsPersonal: boolean = true;
  invalidFields: string[] = [];
  submitted: boolean = false;
  termsChecked: boolean = false;
  Severity = Severity;
  basket$ = computed(() => this.dataStateService.getBasket.state() || {} as State<IGetBasketResponse>);
  private readonly destroy: DestroyRef = inject(DestroyRef);

  constructor(private fb: FormBuilder) {
  }

  ngOnInit(): void {

    this.initGetBacket();

    this.userForm = this.fb.group({
      type: ['receipt', Validators.required],
      firstName: ['', [Validators.required, (control: AbstractControl) => CustomValidators.nameValidator(control, 'First name')]],
      lastName: new FormControl<string>('', [Validators.required, (control: AbstractControl) => CustomValidators.nameValidator(control, 'Last name')]),
      dialCode: new FormControl<IDialCodeDataDto>({} as IDialCodeDataDto, [Validators.required]),
      mobileNumber: new FormControl<string>(''),
      country: new FormControl<string>('', [Validators.required]),
      addressLine1: new FormControl<string>('', [Validators.required]),
      addressLine2: new FormControl<string>(''),
      city: new FormControl<string>('', [Validators.required]),
      state: new FormControl<string>(''),
      postCode: new FormControl<string>('', [Validators.required]),
      vat: [''],
      profession: [''],
      taxOffice: [''],
      emailAddress: ['', Validators.email],
    });



    this.userForm.get('type')?.valueChanges.pipe(takeUntilDestroyed(this.destroy)).subscribe(value => {
      this.toggleInvoiceFields(value === 'invoice');
    });
    this.toggleBillingInformationFields(this.isBillingSameAsPersonal);

    // Subscribe to statusChanges to update invalidFields dynamically
    this.userForm.statusChanges.pipe(takeUntilDestroyed(this.destroy)).subscribe(() => {
      this.invalidFields = this.generalService.findInvalidControls(this.userForm);
    });
    this.getProfileInfo();
  }

  private updatePhoneNumberValidator(): void {
    const mobileNumberControl = this.userForm.get('mobileNumber');
    if (mobileNumberControl) {
      mobileNumberControl.setValidators([this.phoneNumberValidator()]);
      mobileNumberControl.updateValueAndValidity();
    }
  }

  private phoneNumberValidator() {
    return (control: AbstractControl) => CustomValidators.phoneNumberValidator(this.userForm?.get('dialCode')?.value +
      control.value, 'Mobile Number', this.countryCode());
  }

  onBillingInfoChange(isSame: boolean): void {
    this.isBillingSameAsPersonal = isSame;
    if (!isSame) {
      this.resetUserFormToDefaultValues();
    }
    // this.toggleBillingInformationFields(isSame);
  }

  onSubmit(): void {
    console.log(this.userForm);
    this.submitted = true;
    this.generalService.markAllFormFieldsAsTouched(this.userForm);

    if (!this.formErrorScroller.validateAndScrollToFirstError(this.userForm)) {
      console.log('Form is invalid', this.userForm.value);
      return;
    }

    if (this.userForm.valid) {
      this.formSubmit.emit(this.userForm.value);
      this.generalService.showDivLoading(null, 'Processing your order...');
      setTimeout(() => {
        this.checkoutService.goToCheckoutStep(3);
      }, 4000);
    } else {
      this.invalidFields = this.generalService.findInvalidControls(this.userForm);
      this.viewportScroller.setOffset([0, 77]);
      this.viewportScroller.scrollToAnchor('invalid-controls');
      // this.checkoutService.goToCheckoutStep(3);
    }
  }


  ngOnDestroy(): void {
    // Form subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  getProfileInfo(): void {
    this.formLoading.set(true);
    this.apiService.getApiData<IGetProfileInfoResponse>(
      { url: IProfileInfo.getProfileInfo, method: 'GET' },
      { userId: this.user().id }
    ).pipe(takeUntilDestroyed(this.destroy)).subscribe({
      next: (response: IGetProfileInfoResponse) => {
        if (!response) return;
        this.patchFormWithProfileData(response);
        console.log(response);
        this.dataStateService.setStateDirectly(
          this.dataStateService.getUserProfileInfo.setState,
          { data: response, loading: false, error: null, hasError: false }
        );

      },
      error: (err) => {
        console.error('Error fetching profile info', err);
      }
    });
  }

  private patchFormWithProfileData(profileData: IGetProfileInfoResponse): void {
    const basicInfo = profileData.basicProfileInfoDto;
    const mobileNumber = profileData.mobileNumberDto;
    const address = profileData.userAddress;
    const primaryEmail = profileData.emails!.find((email: any) => email.isPrimary);

    this.userForm.patchValue({
      firstName: basicInfo?.firstName || '',
      lastName: basicInfo?.lastName || '',
      emailAddress: primaryEmail?.email || '',
      dialCode: mobileNumber?.dialCodeData || {} as IDialCodeDataDto,
      mobileNumber: mobileNumber?.mobileNumber || '',
      country: address?.country || '',
      addressLine1: address?.addressLine1 || '',
      addressLine2: address?.addressLine2 || '',
      city: address?.city || '',
      state: address?.state || '',
      postCode: address?.postCode || ''
    });
    // Update country code signal
    if (mobileNumber?.dialCodeData?.countryCode) {
      this.countryCode.set(mobileNumber.dialCodeData.countryCode);
    }

    this.generalService.setupDynamicStateValidation(this.userForm, 'country', 'state');
    this.updatePhoneNumberValidator();
    // Update form validity
    setTimeout(() => {
      this.userForm.updateValueAndValidity();
      this.invalidFields = this.generalService.findInvalidControls(this.userForm);
      console.log(this.userForm.value);
      console.log("🚀 ~ CheckoutStepsInfoFormComponent ~ patchFormWithProfileData ~ value:", this.userForm.value)
      console.log("🚀 ~ CheckoutStepsInfoFormComponent ~ patchFormWithProfileData ~ invalidFields:", this.invalidFields)
      if (this.userForm.valid) {
        this.showSummary.set(true);
      }
      this.formLoading.set(false);
    }, 10);


  }

  isInvalid(controlName: string): boolean {
    const control = this.userForm.get(controlName);
    return control?.invalid! && (control.dirty || control.touched) as boolean;
  }

  backToCheckout() {
    this.checkoutService.goToCheckoutStep(1);
  }

  onTermsChange(event: any) {
    this.termsChecked = event.checked;
  }

  editForm(): void {
    this.showSummary.set(false);
  }

  onDialCodeChange(event: IDialCodeDataDto): void {
    console.log(event);
    this.userForm.patchValue({ dialCode: event, mobileNumber: this.userForm.getRawValue().mobileNumber });
    this.userForm.updateValueAndValidity();
    this.userForm.markAsDirty();
  }
  onCountryCodeChange(data: string): void {
    console.log(data);
    this.countryCode.set(data);
    this.userForm.patchValue({ mobileNumberCountryCode: this.countryCode() });
    this.userForm.updateValueAndValidity();
  }

  private initGetBacket() {
    this.apiService.getApiData<IGetBasketResponse>(
      { url: IBasket.getBasket, method: 'GET' },
      {
        parentId: this.authService.getUserBasicInfo()?.userId,
      }).pipe(takeUntilDestroyed(this.destroy))
      .subscribe((response: IGetBasketResponse) => {
        console.log(response);
      });
  }

  private toggleInvoiceFields(isInvoice: boolean): void {
    const vat = this.userForm.get('vat');
    const profession = this.userForm.get('profession');
    const taxOffice = this.userForm.get('taxOffice');
    const email = this.userForm.get('email');

    if (isInvoice) {
      vat?.setValidators([Validators.required]);
      profession?.setValidators([Validators.required]);
      taxOffice?.setValidators([Validators.required]);
      email?.setValidators([Validators.required, Validators.email]);
    } else {
      vat?.clearValidators();
      profession?.clearValidators();
      taxOffice?.clearValidators();
      email?.clearValidators();
    }

    vat?.updateValueAndValidity();
    profession?.updateValueAndValidity();
    taxOffice?.updateValueAndValidity();
    email?.updateValueAndValidity();
  }

  private toggleBillingInformationFields(isBillingInfoSameAsPersonal: boolean): void {
    const fieldsToToggle = ['billingFirstName', 'billingLastName', 'billingAddress1', 'billingAddress2', 'billingCity', 'billingState', 'billingPostalCode'];

    if (isBillingInfoSameAsPersonal) {
      fieldsToToggle.forEach(field => {
        const billingControl = this.userForm.get(field);
        const personalControl = this.userForm.get(field.replace('billing', '').toLowerCase());
        billingControl?.setValue(personalControl?.value);
        billingControl?.disable();
      });
    } else {
      fieldsToToggle.forEach(field => {
        this.userForm.get(field)?.enable();
      });
    }
  }

  private resetUserFormToDefaultValues(): void {
    this.userForm.reset({
      type: 'receipt',
      firstName: '',
      lastName: '',
      dialCode: {} as IDialCodeDataDto,
      mobileNumber: '',
      country: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postCode: '',
      vat: '',
      profession: '',
      taxOffice: '',
      email: '',
    });
  }

}
