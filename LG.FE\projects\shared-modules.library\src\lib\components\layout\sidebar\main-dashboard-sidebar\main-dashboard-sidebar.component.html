<aside class="layout-sidebar {{extraLayoutCssClass}} shadow-1">

  <div class="none md:flex p-2 logo-header w-full align-items-center justify-content-center">
  @if (generalService.miniLayoutSidebar()) {
    <a (click)="authService.goToDashboardPerRole()" class="flex align-items-center cursor-pointer logo-container">
      <img alt="logo" class="logo spiral-logo" ngSrc="/shared-assets/images/shared/spiral-white.svg" width="54" height="34"
        priority>
    </a>
  } @else {
    <a (click)="authService.goToDashboardPerRole()" class="flex align-items-center cursor-pointer">
      <img alt="logo" class="logo" ngSrc="shared-assets/images/shared/lingogalaxy-logo.svg" width="114" height="54" priority>
    </a>
  }
</div>

  @defer(when user()) {

  @defer(on timer(250ms)) {



  <div class="">

<!-- For mini sidebar -->
<!-- For mini sidebar -->
@if (generalService.miniLayoutSidebar()) {
  <div class="mt-0">
    <ul class="list-none p-0 m-0">
      <li *ngFor="let link of navigationLinks()" class="relative">


        <a pRipple 
           [pTooltip]="link.label"
           tooltipPosition="right"
           [routerLink]="!link.items ? link.path : undefined"
           [class.active]="isActive(link.path)"
        class="mini__icon flex flex-row lg:flex-column align-items-center cursor-pointer p-3 justify-content-center border-right-2 border-transparent transition-duration-150 transition-colors no-underline text-center"
           pStyleClass="@next" 
           enterClass="hidden" 
           leaveToClass="hidden"
           [hideOnOutsideClick]="true">
           <i [class]="link.icon + ' text-2xl'" [ngClass]="{'font-bold': isActive(link.path)}"></i>

          @if (link.items) {
          <i class="pi pi-chevron-down ml-auto lg:hidden"></i>
          }
        </a>
        @if (link.items) {
          <ul #submenu class="list-none pl-3 pr-0 py-0 lg:p-2 m-0 hidden overflow-hidden transition-all duration-200 static lg:absolute left-100 top-0 z-1 bg-white shadow-md w-full lg:w-15rem border-1 border-gray-200">
            <li *ngFor="let subItem of link.items">
              <a pRipple 
              (click)="!subItem.items && submenu.classList.add('hidden')"
                 [routerLink]="!subItem.items ? subItem.path : undefined"
                 class="flex align-items-center cursor-pointer px-2 py-2 text-700 hover:bg-purple-50 hover:text-purple-700 transition-colors duration-150 no-underline border-bottom-1 surface-border"
                 pStyleClass="@next" 
                 toggleClass="hidden">
                <i [class]="subItem.icon + ' mr-3 text-purple-600'"></i>
                <span class="font-medium">{{subItem.label}}</span>
                @if (subItem.items) {
                <i class="pi pi-chevron-right ml-auto text-500"></i>
                }
              </a>
              
              @if (subItem.items) {
              <ul class="list-none py-2 pl-4 pr-0 m-0 hidden overflow-hidden transition-all duration-200 bg-gray-50 border-top-1 border-gray-200">
                <li *ngFor="let grandChild of subItem.items">
                  <a pRipple
                     [routerLink]="grandChild.path"
                     class="flex align-items-center cursor-pointer px-3 py-2 text-600 hover:bg-purple-50 hover:text-purple-700 transition-colors duration-150 no-underline">
                    <i [class]="grandChild.icon + ' mr-2 text-purple-500'"></i>
                    <span class="font-medium">{{grandChild.label}}</span>
                  </a>
                </li>
              </ul>
              }
            </li>
          </ul>
        }
      </li>
    </ul>
  </div>
} @else {
  <!-- For expanded sidebar -->
  <ul class="list-none p-0 pt-3 m-0 mt-0">
    <li *ngFor="let link of navigationLinks()">
      <!-- Main menu item -->
      <div class="menu-item">
        <a pRipple
           [routerLink]="!link.items ? link.path : undefined"
           (click)="link.method ? link.method() : (link.items ? toggleSubmenu(link) : generalService.sidebarVisible.set(false))"
           [class.active]="isActive(link.path)"
           [class.active-parent]="isActiveParent(link)"
           class="text-decoration-none flex align-items-center justify-content-between cursor-pointer 
           py-3 px-3 text-sm
            primary-link-nav hover:bg-purple-100 hover:text-900 transition-duration-150 transition-colors">
          <div class="flex align-items-center">
            <i [class]="link.icon + ' mr-2'" [ngClass]="{'font-bold': isActive(link.path)}"></i>
            <span class="font-medium">{{link.label}}</span>
          </div>
          <div class="flex align-items-center">
            <span *ngIf="link.count" class="mr-2 px-2 py-1 border-round-md bg-blue-600 text-white text-sm">{{link.count}}</span>
            <i *ngIf="link.items" class="pi" [ngClass]="link.expanded ? 'pi-chevron-down' : 'pi-chevron-right'"></i>
          </div>
        </a>
      </div>
  
      <!-- Submenu items -->
      @if (link.items) {
        <div class="submenu" [@submenuAnimation]="(link.expanded) ? 'expanded' : 'collapsed'">
        <ul class="list-none py-0 px-0 m-0">
          <li *ngFor="let subItem of link.items">
            <a pRipple
               [routerLink]="!subItem.items ? subItem.path : undefined"
               (click)="!subItem.items ? generalService.sidebarVisible.set(false) : toggleSubmenu(subItem)"
               routerLinkActive="text-primary"
               [class.active]="isActive(subItem.path)"
               [class.active-parent]="isActiveParent(subItem)"
               class="flex align-items-center justify-content-between p-3 pl-5 cursor-pointer primary-link-nav no-underline hover:surface-100 transition-colors transition-duration-150">
              <div class="flex align-items-center">
                <i [class]="subItem.icon + ' mr-2 text-base'"></i>
                <span class="font-medium">{{subItem.label}}</span>
              </div>
              <div class="flex align-items-center">
                <span *ngIf="subItem.count" class="ml-auto px-2 py-1 border-round-md bg-primary-50 text-primary-700 text-sm">{{subItem.count}}</span>
                @if (subItem.items) {
                  <i class="pi ml-2" [ngClass]="subItem.expanded ? 'pi-chevron-down' : 'pi-chevron-right'"></i>
                }
              </div>
            </a>
  
            @if (subItem.items) {
              {{isRouteInSubmenu(subItem)}}
              <div class="submenu" [@submenuAnimation]="(subItem.expanded || isRouteInSubmenu(subItem)) ? 'expanded' : 'collapsed'">
                <ul class="list-none py-0 px-0 m-0">
                  <li *ngFor="let grandChild of subItem.items">
                    <a pRipple
                       [routerLink]="grandChild.path"
                       (click)="generalService.sidebarVisible.set(false)"
                       routerLinkActive="text-primary"
                       [class.active]="isActive(grandChild.path)"
                       class="flex align-items-center p-3 pl-7 cursor-pointer no-underline hover:surface-100 transition-colors transition-duration-150">
                      <i [class]="grandChild.icon + ' mr-2 text-base'"></i>
                      <span class="font-medium">{{grandChild.label}}</span>
                      <span *ngIf="grandChild.count" class="ml-auto px-2 py-1 border-round-md bg-primary-50 text-primary-700 text-sm">{{grandChild.count}}</span>
                    </a>
                  </li>
                </ul>
              </div>
              }
          </li>
        </ul>
      </div>
      }
    </li>
  </ul>
  }


    <!-- {{user$()?.firstName}} {{user$()?.lastName}} -->

  </div>


  @if (user()!.isImpersonated === true) {
  <div class="p-2 mt-auto border-top-1 border-gray-100">

    <div class="pt-0" [pTooltip]="generalService.miniLayoutSidebar() ? 'Back to Parent Account' : undefined">
      <!-- buy-package-grad-btn -->
      <p-button label="" (click)="stopImpersonate()"  [rounded]="true" iconPos="right"
        tooltipPosition="right"
        styleClass="btn-soft-primary w-full text-base py-2" >
        <div class="w-full flex align-items-center justify-content-center gap-2">
          <i class="pi pi-arrow-left"></i>
          @if (!generalService.miniLayoutSidebar()) {
          <span class="font-semibold text-sm">Back to Parent Account</span>
          }
          <!-- <img src="assets/images/rocket.png" alt="logo" height="24"> -->
        </div>
      </p-button>
    </div>

  </div>
  }

  @if (IUserRole.PARENT == user()?.role) {
  <div class="p-2 mt-auto border-top-1 border-gray-100">

    <div class="pt-2">
      <!-- buy-package-grad-btn -->
      <p-button label="" (click)="generalService.goToBuyPackage()" styleClass="modern-btn primary-btn text-base w-full" [rounded]="true"
        iconPos="right">
        <div class="w-full flex align-items-center justify-content-center gap-2 btn-text">
          @if (!generalService.miniLayoutSidebar()) {
          <span class="font-semibold">Buy Package </span>
          }
          <i class="pi pi-shopping-cart"></i>
          <!-- <img src="assets/images/rocket.png" alt="logo" height="24"> -->
        </div>
      </p-button>
    </div>
    <div class="pt-3">
      <p-button (click)="generalService.goToFreeTrialForm()" label="" styleClass="btn-soft-primary w-full text-base py-2" [rounded]="true"
        iconPos="right">
        <div class="w-full flex align-items-center justify-content-center gap-2">
          @if (!generalService.miniLayoutSidebar()) {
          <span class="font-semibold">Free Trial </span>
          }
          <i class="pi pi-graduation-cap"></i>
        </div>
      </p-button>
    </div>

  </div>
  }

  @if (IUserRole.TEACHER == user()?.role) {
  <div class="p-2 mt-auto border-top-1 border-gray-100">

    <div class="pt-2">
      <p-button label="" (click)="generalService.goToBookLesson()" styleClass="modern-btn primary-btn text-base w-full" [rounded]="true"
        iconPos="right">
        <div class="w-full flex align-items-center justify-content-center gap-2">
          <span class="font-semibold">Schedule Lesson </span>
          <i class="pi pi-calendar"></i>
        </div>
      </p-button>
    </div>

  </div>
  }



  <div class="sidebar-collapser-wrapper hidden lg:block">
    <p-button 
      styleClass="sidebar-collapser-button p-button-rounded p-button-text" 

      (click)="generalService.setMiniLayoutSidebar(!generalService.miniLayoutSidebar())"
      [icon]="generalService.miniLayoutSidebar() ? 'pi pi-angle-double-right' : 'pi pi-angle-double-left'"
      pRipple>
      <span class="sidebar-collapser-icon"></span>
    </p-button>
  </div>
  
  }
  @placeholder {

  <div class="p-2 border-top-1 border-gray-100">
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
  </div>
  }

  }@placeholder {

  <div class="p-2 border-top-1 border-gray-100">
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
    <lib-skeleton-loader shape="rectangle" width="w-full" height="2rem"
      styleClass="mb-2 sm:w-full"></lib-skeleton-loader>
  </div>
  }

</aside>

<div #dynamicComponentContainer></div>