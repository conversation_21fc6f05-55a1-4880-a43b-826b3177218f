import { CommonModule, DatePipe } from '@angular/common';
import { Component, ChangeDetectionStrategy, Input, ViewChild, ChangeDetectorRef, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { Table, TableModule } from 'primeng/table';
import { Column } from '../../models/column.model';
import { CommonService } from '../../services/common.service';

@Component({
  selector: 'app-shared-table',
  imports: [
    CommonModule,
    InputTextModule,
    TableModule,
    TagModule,
    IconFieldModule,
    InputIconModule,
    ButtonModule,
  ],
  templateUrl: './shared-table.component.html',
  styleUrls: ['./shared-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharedTableComponent {
  @Input() cols: Column[] = [];
  @Input() rowCount!: number;
  @Input() entityName!: string;
  @Input() addWithDialog = false;

  @Output() onAddClick = new EventEmitter<void>();
  @Output() onEditClick = new EventEmitter<unknown>(undefined);
  @Output() reoderRow = new EventEmitter<unknown[]>(undefined);

  private _tableData!: unknown[];
  @Input() set tableData(value: unknown[]) {
    this._tableData = value;
  }
  get tableData(): unknown[] {
    return this._tableData;
  }

  first = 0;
  rows = 10;

  @ViewChild('sharedTable') sharedTable: Table | undefined;

  get loading() {
    return this.commonService.isLoading$();
  }

  constructor(private commonService: CommonService, private _router: Router) { }

  onRowClick(id: number) {
    if (id) {
      this._router.navigate([`/admin/${this.entityName}`, id]);
    }
  }

  goToAddRoute(): void {
    if (this.addWithDialog) {
      this.onAddClick.emit();
    } else {
      this._router.navigate([`/admin/${this.entityName}/add`]);
    }

  }

  clear(table: Table, input: HTMLInputElement) {
    input.value = '';
    table.clear();
  }

  applyFilterGlobal($event: Event, stringVal: string) {
    this.sharedTable?.filterGlobal(($event.target as HTMLInputElement).value, 'contains');
  }

  onEditSelected(item: unknown) {
    this.onEditClick.emit(item);
  }

  onColReorderTable(event: { dragIndex?: number; dropIndex?: number }) {
    if (event.dragIndex === undefined || event.dropIndex === undefined) {
      console.warn('Invalid reorder event: dragIndex or dropIndex is undefined');
      return;
    }

    let clonedData = [...this.tableData];

    const dragIndex = event.dragIndex + 1; // Zero-based index
    const dropIndex = event.dropIndex + 1;
  
    // Validate indexes
    if (dragIndex < 0 || dragIndex >= this.tableData.length) {
      console.error('Invalid dragIndex:', dragIndex);
      return;
  }
  if (dropIndex < 0 || dropIndex > this.tableData.length) {
      console.error('Invalid dropIndex:', dropIndex);
      return;
  }

  
    let movedRow = clonedData[dragIndex];
    if (!movedRow) {
      console.error('Moved row is undefined for dragIndex:', dragIndex);
      return;
    }
    
    console.log('Moved row before move:', JSON.stringify(movedRow));
  
    // Remove from old position
    clonedData.splice(dragIndex, 1);
  
    // Insert at new position
    clonedData.splice(dropIndex, 0, movedRow);
  
    // 🔹 Update order while maintaining relative changes
    clonedData.forEach((item, index) => {
      if (item && typeof item === 'object' && 'order' in item) {
        (item as { order: number }).order = index + 1; // Ensuring order remains 1-based
      }
    });
  
    // Update table data reference
    this.tableData = [...clonedData];
  
    console.log('Final tableData:', JSON.stringify(this.tableData));
    console.log(`Moved row ${JSON.stringify(movedRow)} from index ${dragIndex} to ${dropIndex}`);
  
    // Emit updated table data
    this.reoderRow.emit(this.tableData);
  }
  
}
