import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, Injector, linkedSignal, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Router, RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { TabMenuModule } from 'primeng/tabmenu';

import { AuthStateService, DataApiStateService, HandleApiResponseService, IGetProfileInfoRequest, IGetProfileInfoResponse, IProfileInfo, State } from 'SharedModules.Library';
import { untilDestroyed } from 'SharedModules.Library';
import { IUserRole } from 'SharedModules.Library';
import { InnerHeaderCardComponent } from '@platform.app/shared/layout/inner-header-card/inner-header-card.component';
import { OverviewCardBlockComponent } from '@platform.app/shared/components/overview-card-block/overview-card-block.component';
import { UserSettingsInfoCardComponent } from '@platform.app/shared/components/user-settings/user-settings-info-card/user-settings-info-card.component';
import { SkeletonLoaderComponent } from 'SharedModules.Library';

@Component({
    selector: 'app-user-settings',
    imports: [
    CommonModule,
    FileUploadModule,
    ButtonModule,
    RouterModule,
    TabMenuModule,
    InnerHeaderCardComponent,
    OverviewCardBlockComponent,
    UserSettingsInfoCardComponent,
    SkeletonLoaderComponent
],
    templateUrl: './user-settings.component.html',
    styleUrl: './user-settings.component.scss'
})
export class UserSettingsComponent implements OnInit, OnDestroy {
    IUserRole = IUserRole;
    breadcrumbs = [
        { label: 'Settings', url: '' },
    ];

    menuItems: { label: string, route: string, icon: string }[] = [
        { label: 'My Account', route: '/dashboard/user-settings/account-info', icon: 'pi-user' },
        { label: 'Password', route: '/dashboard/user-settings/password', icon: 'pi-lock' },
        // { label: 'My Students', route: '/dashboard/user-settings/students-info', icon: 'pi-users' },
        { label: 'My Orders', route: '/dashboard/user-settings/invoices-info', icon: 'pi-receipt' },
        { label: 'Availability', route: '/dashboard/user-settings/availability-info', icon: 'pi-calendar' },
        { label: 'Notification Alerts', route: '/dashboard/user-settings/notification-alerts', icon: 'pi-bell' },
    ];

    user = computed(() => this.authService.userDecodedJWTData$());
    userBasicInfo = linkedSignal(() => this.authService.getUserBasicInfo());
    userRole = computed(() => this.authService.getUserRole());
    uploadedFiles: File[] = [];
    fileObject: SafeUrl | string = 'assets/images/dummy/astronaut-2.webp';
    profilePhotoUrl = signal({} as SafeUrl); // To store the image URL
    activeItem: MenuItem = {}; // This will hold the active item
    private readonly untilDestroyed = untilDestroyed();

    getUserProfileInfo$ = computed(() => {
        return this.dataStateService.getUserProfileInfo.state() || {} as State<IGetProfileInfoRequest>;
    });
    constructor(
        private authService: AuthStateService,
        private dataStateService: DataApiStateService<IGetProfileInfoRequest>,
        private handleApiService: HandleApiResponseService,
        private router: Router,
        private injector: Injector
    ) { }

    ngOnInit(): void {
        // Optional: Manage body class changes if needed
        // this.updateBodyClasses();
        this.initEvents();
        this.setActiveItem();
        this.getProfileInfo();
    }

    ngOnDestroy() {
        this.dataStateService.clearStateDirectly(
            this.dataStateService.getUserProfileInfo.setState,
        );
    }

    isActive(route: string): boolean {
        return this.router.url === route;
    }

    private initEvents(): void {
        // this event is fired when user is logged in
        const userLoggedInSubscription = toObservable(this.userRole, {
            injector: this.injector
        }).pipe(this.untilDestroyed()).subscribe({
            next: (user) => {
                if (!user) {
                    return;
                }
                this.setMenuItemsByRole(user);
                if (user === IUserRole.PARENT) {

                    setTimeout(() => {
                    }, 10);
                    // this.prepareParentStudents();
                }
            }
        });


        const getUserProfileInfo = toObservable(this.getUserProfileInfo$, {
            injector: this.injector
        }).pipe(this.untilDestroyed()).subscribe({
            next: (state) => {
                if (state.loading) {
                    return;
                }
                if (state.data) {
                }
            }
        });

    }

    private setActiveItem(): void {
        const currentRoute = this.router.url;
        this.activeItem = this.menuItems.find((item) => item.route === currentRoute) || { label: '', route: '', icon: '' };
    }

    getProfileInfo(): void {
        this.handleApiService.getApiData<IGetProfileInfoResponse>(
            { url: IProfileInfo.getProfileInfo, method: 'GET' },
            { userId: this.user()?.id }
        ).subscribe({
            next: (response: IGetProfileInfoResponse) => {
                if (!response) return;

                this.dataStateService.setStateDirectly(
                    this.dataStateService.getUserProfileInfo.setState,
                    { data: response, loading: false, error: null, hasError: false }
                );

                this.userBasicInfo.set(response.basicProfileInfoDto);
            },
            error: (err) => {
                console.error('Error fetching profile info', err);
            }
        });
    }

    private setMenuItemsByRole(role: string): void {
        const roleMenus: Record<string, { label: string, route: string, icon: string }[]> = {
            [IUserRole.TEACHER]: [
                { label: 'My Account', route: '/dashboard/user-settings/account-info', icon: 'pi-user' },
                { label: 'My Work Profile', route: '/dashboard/user-settings/work-profile-info', icon: 'pi-id-card' },
                { label: 'Password', route: '/dashboard/user-settings/password', icon: 'pi-lock' },
                { label: 'Notification Alerts', route: '/dashboard/user-settings/notification-alerts', icon: 'pi-bell' }
            ],
            [IUserRole.PARENT]: [
                { label: 'My Account', route: '/dashboard/user-settings/account-info', icon: 'pi-user' },
                { label: 'Password', route: '/dashboard/user-settings/password', icon: 'pi-lock' },
                { label: 'My Orders', route: '/dashboard/user-settings/invoices-info', icon: 'pi-receipt' },
                { label: 'Notification Alerts', route: '/dashboard/user-settings/notification-alerts', icon: 'pi-bell' }
            ],
            [IUserRole.STUDENT]: [
                { label: 'My Account', route: '/dashboard/user-settings/account-info', icon: 'pi-user' },
                { label: 'Availability', route: '/dashboard/user-settings/availability-info', icon: 'pi-calendar' },
                { label: 'Notification Alerts', route: '/dashboard/user-settings/notification-alerts', icon: 'pi-bell' }
            ]
        };

        // Add password option
        // TODO: add check if user is impersonated
        //  && this.authService.getUserClaims().isImpersonated
        // if (role === IUserRole.STUDENT) {
        //     roleMenus[IUserRole.STUDENT].push({ label: 'Password', route: '/dashboard/user-settings/password', icon: 'pi-lock' });
        // }

        this.menuItems = roleMenus[role] || [];
    }

}
