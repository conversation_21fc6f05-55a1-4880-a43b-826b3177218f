import { CommonModule, NgOptimizedImage } from '@angular/common';
import { ChangeDetectionStrategy, Component, Injector, Input, OnInit, Signal, model, signal, input, inject, EventEmitter, Output, computed } from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { IPostUpdateProfilePhotoResponse, IProfileInfo } from '../../../GeneratedTsFiles';
import { HandleApiResponseService } from '../../../services/handle-api-response.service';
import { EmitEvent, EventBusService, Events } from '../../../services/event-bus.service';
import { DialogModule } from 'primeng/dialog';
import { untilDestroyed } from '../../../helpers/until-destroyed';
import { FileUploadModule } from 'primeng/fileupload';
import { GeneralService } from '../../../services/general.service';
import { FileValidationOptions, FileValidationService } from '../../../services/file-validators.service';
import { ToastService } from '../../../services/toast.service';
import { getToastMessage, ToastMessages } from '../../../models/toast-messages';
import { HttpClient, HttpResponse } from '@angular/common/http';

interface FileUploadEvent {
  files: File[];
  currentFiles: File[];
  originalEvent: Event;
}

@Component({
  selector: 'lib-prime-profile-photo-single',
  imports: [CommonModule, NgOptimizedImage, DialogModule, FileUploadModule],
  templateUrl: './prime-profile-photo-single.component.html',
  styleUrl: './prime-profile-photo-single.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PrimeProfilePhotoSingleComponent implements OnInit {
  handleApiResponseService = inject(HandleApiResponseService);
  eventBusService = inject(EventBusService);
  generalService = inject(GeneralService);
  fileValidationService = inject(FileValidationService);
  toastService = inject(ToastService);

  @Input() src: string | undefined | null; // Changed to string
  @Input() width: number = 30;
  @Input() height: number = 30;
  @Input() alt: string = 'Profile Photo';
  @Input() priority: boolean = false;
  @Input() loading: 'lazy' | 'eager' = 'lazy';
  @Input() sizes: string = 'auto';
  @Input() fill: boolean = false;
  @Input() placeholder: string = '';
  @Input() fallbackSrc: string = 'shared-assets/images/shared/people_user_icon.svg';
  @Input() customClass: string = 'border-circle';
  @Input() canuploadDirectlyToApi = true;
  @Input() usePreviewInMainImage: boolean = false;

  @Output() fileUploaded = new EventEmitter<File>();


  imageClass = input('');
  uploadButtonCssClass = input('img-btn');
  canUpload = input(false);
  userId = input('');

  readonly _previewImageUrl = signal<string>(this.fallbackSrc); // New signal for preview
  private readonly _profilePhotoUrl = signal<string>(this.fallbackSrc); // Store as string
  hasPreview = computed(() => this.canUpload() && !!this._previewImageUrl()); // Helper to check if preview should be shown

  readonly isLoading = model<boolean>(false);

  profilePhotoUrl = this._profilePhotoUrl; // Expose signal
  previewImageUrl = this._previewImageUrl; // Expose signal
  uploadedFiles: File[] = [];
  private readonly untilDestroyed = untilDestroyed();
  private injector = inject(Injector);
  isPhotoUploadLoading = signal(false);

  displayedImage = computed(() => {
    if (this.usePreviewInMainImage && this._previewImageUrl() !== this.fallbackSrc) {
      return this._previewImageUrl();
    }
    return this._profilePhotoUrl();
  });

  constructor(private sanitizer: DomSanitizer, private http: HttpClient) { }

  ngOnInit(): void {
    this._profilePhotoUrl.set(this.src || this.fallbackSrc); // Set as plain string
    // this.getProfilePhoto();
    this.eventBusService.on(Events.StateLoadUserProfilePhoto, () => {
      // this.getProfilePhoto();
    });
  }

  getProfilePhoto() {
    // const userId = this.userId() || '';
    // console.log("🚀 ~ PrimeProfilePhotoSingleComponent ~ getProfilePhoto ~ userId:", userId)
    // const uri = `${IProfileInfo.getProfilePhoto.replace('{UserId}', userId)}`;

    // this.isLoading.set(true);

    // this.handleApiResponseService
    //   .getApiData({
    //     url: uri,
    //     method: 'GET',
    //     responseType: 'blob',
    //   }, null, false
    //   ).pipe(this.untilDestroyed())
    //   .subscribe({
    //     next: (response: HttpResponse<Blob>) => {
    //       if (!response) {
    //         this.setPlaceholder();
    //         this.isLoading.set(false);
    //         return;
    //       }
    //       console.log('Profile photo fetched successfully:', response);

    //       const reader = new FileReader();
    //       reader.onloadend = () => {
    //         this._profilePhotoUrl.set(reader.result as string); // Set as plain string
    //         this.isLoading.set(false);
    //       };
    //       reader.readAsDataURL(response.body!);
    //     },
    //     error: (error) => {
    //       console.error('Failed to fetch profile photo:', error);
    //       this.setPlaceholder();
    //       this.isLoading.set(false);
    //     },
    //   });
  }

  setPlaceholder() {
    this._profilePhotoUrl.set(this.fallbackSrc);
  }

  onImageLoad(): void {
    this.isLoading.set(false);
  }

  onImageError(): void {
    this.setPlaceholder();
  }

  _isDialogVisible = false;
  readonly _dialogImageUrl = signal<string | null>(null);

  openDialog(): void {
    this._dialogImageUrl.set(this.profilePhotoUrl());
    this._isDialogVisible = true;
  }

  closeDialog(): void {
    this._isDialogVisible = false;
  }
  clearPreview() {
    this._previewImageUrl.set(this.fallbackSrc);
  }

  onUpload(event: FileUploadEvent) {
    const file: File = event.files[0];
    this.uploadedFiles.push(file);

    // Create preview URL
    const reader = new FileReader();
    reader.onload = () => {
      this._previewImageUrl.set(reader.result as string);
      // If not uploading directly to API and using preview in main image,
      // emit the file immediately
      if (!this.canuploadDirectlyToApi && this.usePreviewInMainImage) {
        this.fileUploaded.emit(file);
      }
    };
    reader.readAsDataURL(file);

    // Only send to API if canuploadDirectlyToApi is true
    if (this.canuploadDirectlyToApi) {
      this.sendFileToApi(file);
    }
  }

  private sendFileToApi(file: File) {
    const options = FileValidationOptions.create().allowAllImages();
    const validationResult = this.fileValidationService.validateFile(file, options);

    if (!validationResult.isValid) {
      this.toastService.show(
        getToastMessage(ToastMessages.UserSettingsSavePassword.error, {
          data: validationResult.errors.join('\n'),
        })
      );
      return;
    }

    this.fileUploaded.emit(file);

    if (this.canuploadDirectlyToApi) {

      this.isLoading.set(true);
      const userId = this.userId() || '';
      const formData = new FormData();
      formData.append('ProfilePhoto', file, file.name);

      this.handleApiResponseService
        .getApiData<IPostUpdateProfilePhotoResponse>(
          {
            url: `${IProfileInfo.postUpdateProfilePhoto.replace('{UserId}', userId)}`,
            method: 'POST',
          },
          formData
        ).pipe(this.untilDestroyed())
        .subscribe({
          next: (response: IPostUpdateProfilePhotoResponse) => {
            console.log('File uploaded successfully:', response);
            this.isLoading.set(false);
            setTimeout(() => {
              this.generalService.setUploadingProgress(0);
              this._profilePhotoUrl.set(response.profilePhotoUrl || this.fallbackSrc); // Set as plain string
              // this.getProfilePhoto();
              if (this.usePreviewInMainImage) {
                this._previewImageUrl.set(this.fallbackSrc);
              }
            }, 50);
            this.handleApiResponseService.clearProfilePhotoCache(userId);
            this.eventBusService.emit(new EmitEvent(Events.StateLoadUserProfilePhoto, undefined));
          },
          error: (error) => {
            console.error('Upload failed:', error);
            this.generalService.setUploadingProgress(0);
            this.isLoading.set(false);
            this._previewImageUrl.set(this.fallbackSrc);
          },
        });
    }
  }
}