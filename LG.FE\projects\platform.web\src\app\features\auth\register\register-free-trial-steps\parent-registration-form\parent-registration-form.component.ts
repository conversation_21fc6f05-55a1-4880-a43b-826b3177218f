import {
  IApiResponseBase,
  ICompleteParentRegistrationRequest,
  ICompleteParentRegistrationResponse,
  ICountry,
  IdentityRoutes,
  IDialCodeDataDto,
  IGetTimezonesResponse,
  ILoginResponse,
  IRegisterParentRequest,
  IRegisterParentResponse,
  IRegistrationTypeEnum,
  ITimezoneData,
  IWhereDidYouHearAboutUsDto
} from 'SharedModules.Library';
import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  EventEmitter,
  inject,
  Output,
  signal,
  WritableSignal
} from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { switchMap, tap } from 'rxjs';
import { SocialUser } from '@abacritt/angularx-social-login';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouterModule, Router, NavigationExtras } from '@angular/router';
import { InputNumberModule } from 'primeng/inputnumber';
import { TooltipModule } from 'primeng/tooltip';
import { CustomValidators } from '@platform.src/app/core/helpers/custom-validators';
import { untilDestroyed } from 'SharedModules.Library';
import { Severity } from 'SharedModules.Library';
import { HandleApiResponseService } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { DataApiStateService } from 'SharedModules.Library';
import { EventBusService, EmitEvent, Events } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services/register.service';
import { UserService } from '@platform.src/app/core/services/user.service';
import { CardSplitLayoutComponent } from '@platform.src/app/shared/components/card-split-layout/card-split-layout.component';

import { SignupExistingAccountDialogComponent } from '@platform.src/app/shared/components/dialogs/signup-existing-account-dialog/signup-existing-account-dialog.component';
import { GoogleSigninButtonComponent } from '@platform.src/app/shared/components/google-signin-button/google-signin-button.component';
import { FormFieldValidationMessageComponent } from 'SharedModules.Library';
import { PrimeReactiveFormInputComponent } from 'SharedModules.Library';

import { ToastService } from 'SharedModules.Library';

@Component({
  selector: 'app-parent-registration-form',
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,
    CheckboxModule,
    DividerModule,
    InputTextModule,
    ButtonModule,
    DropdownModule,
    InputNumberModule,
    TooltipModule,
    GoogleSigninButtonComponent,
    FormFieldValidationMessageComponent,
    PrimeReactiveFormInputComponent,
    CardSplitLayoutComponent
  ],
  templateUrl: './parent-registration-form.component.html',
  styleUrl: './parent-registration-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: []
})
export class ParentRegistrationFormComponent {

  public reenableButton = new EventEmitter<boolean>(false);
  @Output() submitParentFormButtonClick = new EventEmitter<any>();
  apiService = inject(HandleApiResponseService);
  userService = inject(UserService);
  authService = inject(AuthStateService);
  toastService = inject(ToastService);
  registerService = inject(RegisterService);
  generalService = inject(GeneralService);
  eventBusService = inject(EventBusService);
  router = inject(Router);
  Severity = Severity;
  selectedStudentsNum = 0;
  mappedPhonecodes = signal([]);
  selectedCountry: any;
  userForm: FormGroup = new FormGroup({});
  countriesData: Record<string, any> = {};
  timezones = signal<ITimezoneData[]>([]);
  selectedTimezone = '';
  countries = signal([] as ICountry[]);
  phoneCodes: WritableSignal<IDialCodeDataDto[]> = signal([]);
  phoneCodesUnique = computed(() => {
    const uniqueDialCodesMap = new Map();
    this.phoneCodes().forEach((item: IDialCodeDataDto) => {
      uniqueDialCodesMap.set(item.dialCode, item.country);
    });
    const uniqueDialCodes = Array.from(uniqueDialCodesMap, ([dialCode, country]) => ({ dialCode, country }));
    return [...new Set(uniqueDialCodes)] as IDialCodeDataDto[];
  });
  selectedCountryCode = signal({} as string);
  submitButton = signal(true);
  referralOptions = signal([] as IWhereDidYouHearAboutUsDto[]);
  countryCode = signal('');
  buttonDisabled = signal(false);
  loadingTimezones = signal(true);
  showUserExistsPopupAfterError = signal(false);
  googleLoginWrapperRef?: { click: () => void; destroy: () => void };
  private dataStateService = inject(DataApiStateService);
  private untilDestroyed = untilDestroyed();
  private readonly destroy: DestroyRef = inject(DestroyRef);

  constructor(private fb: FormBuilder) {
  }

  ngOnInit(): void {
    this.initializeForm();
    // this.handleCountriesRequest();
    // this.eventBusService.emit(new EmitEvent(Events.StateLoadGeoLocationData, {}));
    // this.eventBusService.emit(new EmitEvent(Events.StateLoadCountries, undefined));
  }

  ngOnDestroy() {
    // if (this.googleLoginWrapperRef) {
    //   this.googleLoginWrapperRef?.destroy();
    // }
  }

  googleSignin(googleWrapper: any) {
    this.googleLoginWrapperRef = googleWrapper;
    googleWrapper.click();
  }

  registerParent() {
    this.validateFormAndSendRequest();
  }

  onTimezoneChange(event: any) {
    console.log(event);
    // this.selectedTimezoneSignal.set(event.value);
  }

  onDialCodeChange(countryCode: string) {
    console.log(countryCode);
    this.countryCode.set(countryCode);
    this.userForm.patchValue({ mobileNumberCountryCode: this.countryCode() });
    this.userForm.get('mobileNumber')?.updateValueAndValidity();
  }

  onGoogleButtonResponse(event: SocialUser) {
    console.log(event);
    const cd = this.createGoogleRegisterParentRequest(event);
    this.apiService.getApiData<ICompleteParentRegistrationResponse>(
      { url: IdentityRoutes.postRegisterParent, method: 'POST' },
      cd
    ).pipe(this.untilDestroyed()).subscribe({
      next: (response: ICompleteParentRegistrationResponse) => this.handleLoginWithGoogleSuccess(response),
      error: (error) => {
        console.log(error);
      }
    });
  }

  private handleLoginWithGoogleSuccess(response: ICompleteParentRegistrationResponse) {
    console.log(response);
    this.authService.handleUserDataAndDecodeJWT(response);
    this.eventBusService.emit(new EmitEvent(Events.UserLoggedIn,
      { user: this.authService.getUserClaims(), hasSetPassword: false }));
    this.authService.goToDashboardPerRole(); // Or use this.router.navigate(['/dashboard/parent']); if needed
  }

  private initializeForm(): void {
    this.userForm = this.fb.group({
      firstName: ['', [Validators.required, (control: AbstractControl) => CustomValidators.nameValidator(control, 'First name')]],
      lastName: new FormControl<string>('', [Validators.required, (control: AbstractControl) => CustomValidators.nameValidator(control, 'Last name')]),
      emailAddress: new FormControl<string>('', [Validators.required, Validators.email]),
    });
  }

  private validateFormAndSendRequest() {
    this.markFormControlsAsTouchedAndDirty();
    this.logInvalidFormControls();
    console.log(this.userForm.value);
    if (this.userForm.invalid) {
      return;
    }

    const registerParentRequest = this.createRegisterParentRequest();
    this.showUserExistsPopupAfterError.set(false);
    this.sendRegisterParentRequest(registerParentRequest);
  }

  private markFormControlsAsTouchedAndDirty() {
    Object.values(this.userForm.controls).forEach(control => {
      control.markAsTouched();
      control.markAsDirty();
    });
  }

  private logInvalidFormControls() {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      if (control instanceof FormControl && control.invalid) {
        console.log(`Field '${key}' is invalid because:`, control.errors);
        this.userForm.get(key)!.markAsTouched();
        this.userForm.get(key)!.setErrors({ 'invalid': true });
      }
    });
  }

  private createRegisterParentRequest(): IRegisterParentRequest {
    const formValues = this.userForm.value;
    return {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      emailAddress: formValues.emailAddress,
      googleIdToken: '',
      registrationType: IRegistrationTypeEnum.Otp
    };
  }

  private createGoogleRegisterParentRequest(data: SocialUser): IRegisterParentRequest {
    const formValues = this.userForm.value;
    return {
      firstName: '',
      lastName: '',
      emailAddress: '',
      googleIdToken: data.idToken,
      registrationType: IRegistrationTypeEnum.Google
    };
  }

  private sendRegisterParentRequest(registerParentRequest: IRegisterParentRequest) {
    this.buttonDisabled.set(true);
    this.userService.registerParent(registerParentRequest)
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: this.handleRegisterParentResponse,
        error: this.handleRegisterParentError
      }
      );
  }

  private handleRegisterParentError = (error: IApiResponseBase) => {
    console.log(error);
    this.buttonDisabled.set(false);
    if (error.statusCode === 400) {
      this.generalService.openComponent(SignupExistingAccountDialogComponent, {});
    } else {
      this.generalService.handleRouteError(error, IdentityRoutes.postRegisterParent);
    }
  }

  private handleRegisterParentResponse = (response: IRegisterParentResponse) => {
    const dataResponse = (response);
    this.updateStateAndNavigate(dataResponse);
  }

  private updateStateAndNavigate(response: IRegisterParentResponse) {
    this.registerService.updateRegisterParentRequest(this.createRegisterParentRequest());


    // TODO: add user claims
    //this.registerService.updateRegisterStudentRequest({ parentId: response.id });
    //this.authService.setUser(response);
    this.navigateToNextStepWithData('register-otp');
  }

  private navigateToNextStepWithData(route: string) {
    const dataToSend = 'your-data';
    const navigationExtras: NavigationExtras = {
      state: { data: dataToSend }
    };
    this.generalService.navigateTo('auth/register-otp', navigationExtras);
  }

  private handleLoginWithGoogleError(error: any, event: SocialUser) {
    console.log(error);
    if (error.statusCode === 400 && error.data && !error.data.isNotRegisteredWithGoogle) {
      const navigationExtras: NavigationExtras = { state: { data: event } };
      this.registerService.navigateToNextStep('social-auth', navigationExtras);
    }
  }

  private getTimezone() {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
}
