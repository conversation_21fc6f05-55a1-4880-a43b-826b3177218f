import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, ElementRef, inject, input, Input, model, Renderer2, signal, ViewChild, ViewContainerRef, type OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, DateSelectArg, EventApi } from '@fullcalendar/core';
import { ButtonModule } from 'primeng/button';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { TimelineModule } from 'primeng/timeline';
import { ToolbarModule } from 'primeng/toolbar';

import moment from 'moment-timezone';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { TagModule } from 'primeng/tag';
import { CardModule } from 'primeng/card';
import { ConfirmDialog, ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService } from 'primeng/api';
import { AccordionModule } from 'primeng/accordion';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { InputSwitchModule } from 'primeng/inputswitch';
import { Subscription, take } from 'rxjs';
import { CustomConfirmDialogComponent } from '../custom-confirm-dialog/custom-confirm-dialog.component';
import { MobileBottomSheetComponent } from '../mobile-bottom-sheet/mobile-bottom-sheet.component';
import { HandleApiResponseService } from '../../services/handle-api-response.service';
import { EventBusService, Events } from '../../services/event-bus.service';
import { ToastService } from '../../services/toast.service';
import { AuthStateService } from '../../services/auth-state.service';
import { getToastMessage, ToastMessages } from '../../models/toast-messages';
import { IAvailability, IBasicProfileInfoDto, IDayOffByYear, IDayOffRequestStatusEnum, IGetDaysOffRequest, IGetDaysOffResponse } from '../../GeneratedTsFiles';
import { DaysOffDialogComponent } from '../dialogs/days-off-dialog/days-off-dialog.component';
import { GeneralService } from '../../services/general.service';
import { TimezoneService } from '../../services/timezone.service';
import { untilDestroyed } from '../../helpers/until-destroyed';
import { DataApiStateService, State } from '../../services/data-api-state.service';
import { IPostProcessDayOffRequest, IPostRemoveDayOffRequest, ITeacherDayOffDto } from '../../GeneratedTsFiles';
import { nameOf } from '../../models/general.model';

const ITeacherDayOffDtoParamsMap = nameOf<ITeacherDayOffDto>();
interface YearlyDaysOff {
  year: number;
  allowedDaysOffCount: number;
  requests: DayOffRequest[];
}

interface DayOffRequest {
  requestId: string;
  dates: Date[];
  status: number;
  requestedOn: Date;
  rejectedOn?: Date;
  approvedOn?: Date;
  year: number;
}

interface CalendarEvent {
  title: string;
  start: Date;
  end: Date;
  color: string;
  extendedProps: {
    request: DayOffRequest;
    requestId: string;
    status: IDayOffRequestStatusEnum;
    requestedOn: Date;
    rejectedOn?: Date;
    approvedOn?: Date;
    daysCount: number;
    statusLabel: string;
  };
}


interface StatusConfig {
  label: string;
  severity: string;
  color: string;
}

@Component({
  selector: 'app-teacher-availability-days-off',
  imports: [
    CommonModule,
    FormsModule,
    FullCalendarModule,
    ScrollPanelModule,
    CardModule,
    ToolbarModule,
    TimelineModule,
    ButtonModule,
    TagModule,
    CheckboxModule,
    ConfirmDialogModule,
    ConfirmDialog,
    AccordionModule,
    InputSwitchModule,
    MobileBottomSheetComponent,
  ],
  templateUrl: './teacher-availability-days-off.component.html',
  styleUrl: './teacher-availability-days-off.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ConfirmationService]
})
export class TeacherAvailabilityDaysOffComponent implements OnInit {
  @Input() userProfileInfo = {} as IBasicProfileInfoDto | null;
  @Input() isAdminView = false;
  // ViewChild decorators
  @ViewChild('calendar', { static: true }) calendarComponent!: FullCalendarComponent;
  @ViewChild('dynamicCContainer', { read: ViewContainerRef, static: true }) dynamicComponentContainer!: ViewContainerRef;
  @ViewChild('fcDays') fcDays: ElementRef[] = [];
  bottomSheetExpanded = false;
  // Dependency injection
  private readonly destroy: DestroyRef = inject(DestroyRef);
  private readonly apiService = inject(HandleApiResponseService);
  private readonly eventBusService = inject(EventBusService);
  private readonly toastService = inject(ToastService);
  private readonly confirmationService = inject(ConfirmationService);
  private readonly authService = inject(AuthStateService);
  private readonly generalService = inject(GeneralService);
  private readonly dataStateService = inject(DataApiStateService);
  readonly timezoneService = inject(TimezoneService);
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly renderer = inject(Renderer2);
  private readonly untilDestroyed = untilDestroyed();
  private daysOffSubscription?: Subscription;

  @ViewChild('bottomSheet') bottomSheetElement!: ElementRef;
  @ViewChild('bottomSheetHandle') bottomSheetHandleElement!: ElementRef;
  @ViewChild('bottomSheetHeader') bottomSheetHeaderElement!: ElementRef;

  // Component state
  private confirmationDialog?: CustomConfirmDialogComponent;
  currentEvents: CalendarEvent[] = [];
  events: CalendarEvent[] = [];
  calendarOptions: CalendarOptions = {};
  currentMonth = signal('');
  isTabletOrMobile = signal(false);
  daysOffRequests = signal<DayOffRequest[]>([]);
  remainingDays = signal(0);
  showRejected = model(true);
  yearlyDaysOff = signal<YearlyDaysOff[]>([]);
  private useQueryParams = false;

  private allYearlyDaysOff = signal<YearlyDaysOff[]>([]);
  private filteredYearlyDaysOff = signal<YearlyDaysOff[]>([]);

  userTimezone = '';
  localtime = '';
  selectedYear = 0;

  getUserProfileInfo$ = computed(() => {
    return this.authService.getUserProfileInfo();
  });
  // Constants
  // New constant for the format string compatible with moment.js
  private readonly DISPLAY_DATE_FORMAT_STRING = ' MMM DD, YYYY';

  private readonly DATE_FORMAT_OPTIONS = {
    weekday: 'short',
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  } as const;

  private readonly STATUS_CONFIG: Record<number, StatusConfig> = {
    [IDayOffRequestStatusEnum.Pending]: {
      label: 'Pending',
      severity: 'warn',
      color: 'var(--p-tag-warn-background)'
    },
    [IDayOffRequestStatusEnum.Approved]: {
      label: 'Approved',
      severity: 'success',
      color: 'var(--p-tag-success-background)'
    },
    [IDayOffRequestStatusEnum.Rejected]: {
      label: 'Rejected',
      severity: 'danger',
      color: 'var(--p-tag-danger-background)'
    }
  };

  ngOnInit(): void {
    // Initialize with today's date by default
    const initialDate = new Date();
    this.userTimezone = this.userProfileInfo?.timeZoneIana!;
    this.localtime = this.timezoneService.getCurrentTimeInZone(this.userTimezone).toLocaleString();
    this.selectedYear = moment().utc().tz(this.userTimezone).year();
    console.log(this.userProfileInfo);
    this.timezoneService.setTimezone(this.userTimezone);


    // Only process query params if enabled
    if (this.useQueryParams) {
      this.route.queryParams.pipe(take(1)).subscribe(params => {
        const month = params['month'];
        const year = params['year'];

        if (month && year) {
          const date = moment.tz(`${year}-${month}-01`, this.localtime);
          if (date.isValid()) {
            initialDate.setTime(date.valueOf());
          }
        }
      });
    }

    this.initializeDaysOffCalendarOptions(initialDate);
    this.getDaysOff();
    this.subscribeToEvents();
  }

  toggleBottomSheet() {
    this.bottomSheetExpanded = !this.bottomSheetExpanded;
    document.body.classList.toggle('mobile-bottom-sheet-open', this.bottomSheetExpanded);
  }

  // Update ngOnDestroy to clean up
  ngOnDestroy() {
    document.body.classList.remove('mobile-bottom-sheet-open');
    this.stopDaysOffEvents();
  }

  private subscribeToEvents(): void {
    this.daysOffSubscription = this.eventBusService.on(Events.StateLoadGetDaysOff, () => {
      this.getDaysOff();
    });
  }

  // Add this method to unsubscribe
  public stopDaysOffEvents(): void {
    if (this.daysOffSubscription) {
      this.daysOffSubscription.unsubscribe();
      this.daysOffSubscription = undefined;
    }
    // Alternative: use the EventBusService's off method
    this.eventBusService.off(Events.StateLoadGetDaysOff);
  }

  private formatDate(date: Date): string {
    // This is used for generating HTML content for the confirmation dialog
    // It should now correctly use the TimezoneService with the correct arguments.
    return this.timezoneService.formatInTimezone(date, this.DISPLAY_DATE_FORMAT_STRING, this.userTimezone);
  }

  private resetState(): void {
    this.events = [];
    this.currentEvents = [];
    this.daysOffRequests.set([]);
    this.yearlyDaysOff.set([]);
  }

  private updateCalendar(): void {
    const calendarApi = this.calendarComponent.getApi();
    calendarApi.removeAllEvents();
    calendarApi.addEventSource(this.events);
    calendarApi.render();
  }

  private initializeDaysOffCalendarOptions(initialDate: Date): void {
    const userTimeZone = this.userTimezone;

    const today = moment.utc().tz(userTimeZone).startOf('day');
    const tomorrow = this.timezoneService.addDuration(today.toDate(), 1, 'days');
    const yesterday = this.timezoneService.subtractDuration(today.toDate(), 2, 'days');
    const twoWeeksFromToday = this.timezoneService.addDuration(today.toDate(), 14, 'days');
    const todayDate = new Date(today.year(), today.month(), today.date());

    this.calendarOptions = {
      plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
      dayHeaderFormat: { weekday: 'short' },
      timeZone: userTimeZone,
      now: today.format('YYYY-MM-DD HH:mm:ss'),
      headerToolbar: {
        left: 'prev',
        center: 'title',
        right: 'next',
      },
      views: {
        timeGridThreeDay: {
          type: 'timeGrid',
          dayCount: 3,
        },
      },
      weekends: true,
      lazyFetching: true,
      allDaySlot: false,
      selectable: true,
      selectConstraint: {
        start: this.isAdminView ? today.toDate() : tomorrow,
      },
      dayCellDidMount: this.handleDayCellMount,
      selectOverlap: this.handleSelectOverlap,
      datesSet: (dateInfo) => {
        // Only update URL if the month/year actually changed
        const currentParams = this.route.snapshot.queryParams;
        const newMonth = (dateInfo.view.currentStart.getMonth() + 1).toString().padStart(2, '0');
        const newYear = dateInfo.view.currentStart.getFullYear().toString();

        if (currentParams['month'] !== newMonth || currentParams['year'] !== newYear) {
          this.updateUrlWithDate(dateInfo.view.currentStart);
        }
      },
      selectMirror: false,
      slotDuration: '00:15:00',
      slotLabelInterval: 15,
      slotLabelFormat: [
        {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          omitZeroMinute: false,
          meridiem: 'short',
        },
      ],
      eventTimeFormat: {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      },
      dayMaxEvents: false,
      dayMaxEventRows: false,
      eventShortHeight: 50,
      slotMinTime: '00:00:00',
      scrollTime: '17:00:00',
      firstDay: 1,
      height: window.innerHeight - 250,
      contentHeight: '400',
      longPressDelay: 110,
      eventLongPressDelay: 110,
      select: (selectionInfo) => this.handleDateSelection(selectionInfo, tomorrow, twoWeeksFromToday),
      eventClick: (clickInfo) => this.handleEventClick(clickInfo),
      eventContent: this.generateEventContent,
      eventClassNames: 'shadow-1 border-round-lg',
      eventDisplay: 'block',
      eventBackgroundColor: 'var(--surface-card)',
      eventBorderColor: 'transparent',
      eventTextColor: 'var(--text-color)',
    };
  }

  // Add this method to update URL when calendar date changes
  private updateUrlWithDate(date: Date): void {
    if (!this.useQueryParams) return;
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { month, year },
      queryParamsHandling: 'merge'
    });
  }
  private handleDayCellMount = (arg: any): void => {
    // Get today's date at start of day in user's timezone
    const today = this.timezoneService.getStartOf(
      this.timezoneService.getCurrentTimeInZone(this.userTimezone),
      'day',
      this.userTimezone
    );

    console.log(this.timezoneService.formatInTimezone(today, this.userTimezone));
    // Convert cell date to user's timezone and get start of day
    const cellDate = this.timezoneService.getStartOf(
      this.timezoneService.convertToLocalTime(arg.date, this.userTimezone),
      'day',
      this.userTimezone
    );

    // Compare timestamps for accurate date comparison
    const todayTime = today.getTime();
    const cellTime = cellDate.getTime();

    if (cellTime <= todayTime) {
      arg.el.classList.add('fc-day-past');
    } else if (cellTime === todayTime) {
      arg.el.classList.add('fc-day-today');
    } else {
      arg.el.classList.add('fc-day-future');
    }
  }

  private handleSelectOverlap = (event: EventApi | null): boolean => {
    const paramsMap = nameOf<ITeacherDayOffDto>();
    if (!event) return true;
    return event.extendedProps?.[paramsMap.status] !== IDayOffRequestStatusEnum.Pending;
  }

  private handleDateSelection(selectionInfo: DateSelectArg, tomorrow: Date, twoWeeksFromToday: Date): void {
    const selectedStartDate = selectionInfo.start;
    const selectedEndDate = new Date(selectionInfo.end);
    const selectedYear = selectedStartDate.getFullYear();
    const diffTime = Math.abs(selectedEndDate.getTime() - selectedStartDate.getTime());
    const daysSelected = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const yearData = this.yearlyDaysOff().find(y => y.year === selectedYear);
    selectedEndDate.setDate(selectedEndDate.getDate() - 1);

    // Validation checks
    if (this.validateDateSelection(selectedStartDate, selectedEndDate, tomorrow, twoWeeksFromToday, daysSelected, yearData)) {
      return;
    }

    // For single day selection, use the same date for both start and end
    const isSingleDay = selectedStartDate.toDateString() === selectedEndDate.toDateString();
    const eventEndDate = isSingleDay ? selectedStartDate : new Date(selectedEndDate);

    // Add the selected range as a new event
    const newEvent = {
      title: 'Days OffA',
      start: selectedStartDate,
      end: eventEndDate,
      color: '#D9534F',
    };

    this.openDaysOffDialog(newEvent);
    this.calendarComponent.getApi().unselect();
  }

  private validateDateSelection(
    startDate: Date,
    endDate: Date,
    tomorrow: Date,
    twoWeeksFromToday: Date,
    daysSelected: number,
    yearData?: YearlyDaysOff
  ): boolean {
    // Skip two weeks validation for admin
    if (!this.isAdminView) {
      // Check if the selected start date is today or in the past
      if (startDate < tomorrow) {
        this.toastService.show(ToastMessages.DaysOff.NoDaysOffCrossYearError); // Assuming this is the correct toast for "past date"
        return true;
      }

      const userTimeZone = this.userTimezone;
      if (!this.isEarliestDayAfterMinimumNotice([startDate], this.isAdminView, userTimeZone)) {
        const nextAvailableDate = new Date(twoWeeksFromToday);
        nextAvailableDate.setDate(nextAvailableDate.getDate() + 1);

        this.toastService.show({
          severity: 'info',
          summary: 'Short Notice',
          position: 'top-right',
          detail: `Requests within two weeks cannot be selected. Next available date: ${this.formatDate(nextAvailableDate)}`
        });
        return true;
      }
      // Check if selection is within two weeks
      // This check might be redundant if isEarliestDayAfterMinimumNotice already covers it.
      // If it's intended to be a separate check for `startDate <= twoWeeksFromToday` without the minimum notice logic, keep it.
      if (startDate <= twoWeeksFromToday) {
        const nextAvailableDate = new Date(twoWeeksFromToday);
        nextAvailableDate.setDate(nextAvailableDate.getDate() + 1);

        this.toastService.show({
          severity: 'info',
          summary: 'Short Notice',
          position: 'top-right',
          detail: `Requests within two weeks cannot be selected. Next available date: ${this.formatDate(nextAvailableDate)}`
        });
        return true;
      }

    }

    if (startDate.getFullYear() !== endDate.getFullYear()) {
      this.toastService.show(ToastMessages.DaysOff.NoDaysOffCrossYearError);
      return true;
    }

    if (!this.isAdminView) {
      if (!yearData || this.getDaysOffCounts(yearData.year).remaining === 0) {
        this.toastService.show(ToastMessages.DaysOff.NoDaysOffRemainingError);
        return true;
      }

      if (daysSelected > this.getDaysOffCounts(yearData.year).remaining) {
        this.toastService.show(
          getToastMessage(ToastMessages.DaysOff.RemainingDaysError, {
            data: `${this.getDaysOffCounts(yearData.year).remaining}`,
          })
        );
        return true;
      }
    }

    return false;
  }

  private handleEventClick(clickInfo: { event: EventApi }): void {
    const event = clickInfo.event;
    const request = event.extendedProps['request'] as DayOffRequest;
    if (
      (request.status === IDayOffRequestStatusEnum.Rejected || request.status === IDayOffRequestStatusEnum.Approved)
      && !this.isAdminView
    ) {
      this.toastService.show(
        ToastMessages.DaysOff.DaysOffRejectedCannotEditError
      );
      return;
    }
    this.openConfirmDialogRemoveDaysOff(request);
  }

  private generateEventContent = (arg: any) => {
    const eventEl = document.createElement('div');
    eventEl.className = 'flex align-items-center gap-2 p-1 w-full';

    const contentEl = document.createElement('div');
    contentEl.className = 'flex flex-column gap-1';

    const titleEl = document.createElement('div');
    titleEl.className = 'flex align-items-center gap-1 text-white';
    titleEl.innerHTML = `
      <i class="pi pi-calendar text-sm"></i>
      <span class="font-medium">${arg.event.title}</span>
    `;

    const timeEl = document.createElement('span');
    timeEl.className = 'text-xs text-500';

    contentEl.appendChild(titleEl);
    contentEl.appendChild(timeEl);
    eventEl.appendChild(contentEl);

    return { domNodes: [eventEl] };
  }

  // Template generation helpers
  private generateDateDisplay(label: string, date: Date): string {
    // This is used for generating HTML content for the confirmation dialog
    return `
      <div class="text-center">
        <div class="text-600 text-sm">${label}</div>
        <div class="text-primary font-medium">${this.formatDate(date)}</div>
      </div>
    `;
  }

  private generateRequestInfo(date: Date): string {
    // This is used for generating HTML content for the confirmation dialog
    return `
      <div class="flex align-items-center gap-2">
        <i class="pi pi-clock text-500"></i>
        <span class="text-600 text-sm">Requested on ${this.formatDate(date)}</span>
      </div>
    `;
  }

  private generateDurationBadge(daysCount: number): string {
    return `
      <div class="surface-200 border-round px-2 py-1">
        <span class="text-600 font-medium">
          ${daysCount} ${daysCount === 1 ? 'day' : 'days'}
        </span>
      </div>
    `;
  }

  private generateConfirmationMessage(): string {
    return '<div class="text-700">Are you sure you want to cancel this request?</div>';
  }

  // Public methods
  getDaysOff(): void {
    const userTimeZone = this.userTimezone;
    const localtime = userTimeZone || moment.tz.guess();
    const currentYear = moment().utc().tz(localtime).year();
    const years = [currentYear, currentYear + 1];
    const params = new URLSearchParams();

    params.append('TeacherId', this.userProfileInfo?.userId!);
    years.forEach(year => params.append('Years', year.toString()));

    this.apiService.getApiData<IGetDaysOffResponse>(
      { url: `${IAvailability.getDaysOff}?${params.toString()}`, method: 'GET' },
      undefined,
      true
    ).pipe(this.untilDestroyed()).subscribe({
      next: (res: IGetDaysOffResponse) => {
        if (res) {
          this.resetState();
          console.log('Days Off Response: ', res);
          // Directly transform and set the signal
          const requests = this.transformDaysOffData(res);
          this.daysOffRequests.set(requests);
          this.allYearlyDaysOff.set(this.yearlyDaysOff()); // Ensure this is set based on the result of transformDaysOffData
          this.events = this.createCalendarEvents(res); // Ensure calendar events are created from the original response

          this.updateCalendar();


          this.cdr.detectChanges();
        }
      },
      error: (err) => {
        console.error('Failed to fetch days off:', err);
        this.resetState();
        this.remainingDays.set(0);
        this.cdr.detectChanges();
      },
    });
  }

  filterRequests(): void {
    const filtered = this.allYearlyDaysOff().map(year => ({
      ...year,
      requests: year.requests.filter(request =>
        this.showRejected() || request.status !== IDayOffRequestStatusEnum.Rejected
      )
    }));

    this.yearlyDaysOff.set(filtered);

    const paramsMap = nameOf<ITeacherDayOffDto>();
    // Create new calendar events from the filtered data with timezone handling
    const calendarData = {
      daysOffByYear: filtered.map(year => ({
        year: year.year,
        dayOffs: year.requests.flatMap((request: DayOffRequest) =>
          request.dates.map((date: Date) => ({ // Ensure 'date' is typed as Date here
            requestId: request[paramsMap.requestId],
            status: request[paramsMap.status],
            date: date, // 'date' is already converted in transformDaysOffData
            requestedOn: request.requestedOn, // already converted in transformDaysOffData
            rejectedOn: request.rejectedOn,   // already converted in transformDaysOffData
            approvedOn: request.approvedOn    // already converted in transformDaysOffData
          }))
        )
      }))
    };

    this.events = this.createCalendarEvents({
      teacherId: this.userProfileInfo?.userId!,
      daysOffByYear: calendarData.daysOffByYear
    } as IGetDaysOffResponse);
    this.updateCalendar();
    this.cdr.detectChanges();
  }

  openConfirmDialogRemoveDaysOff(request: any): void {
    const startDate = request.dates[0]; // These are already Date objects in user's timezone
    const endDate = request.dates[request.dates.length - 1]; // These are already Date objects in user's timezone
    const requestDate = request.requestedOn; // This is already a Date object in user's timezone
    const isSingleDay = request.dates.length === 1;

    const singleDayTemplate = `
      <div class="flex flex-column gap-2">
        <div class="surface-card p-2 border-round">
          <div class="flex align-items-center justify-content-between">
            <div class="flex align-items-center gap-2">
              <i class="pi pi-calendar text-primary"></i>
              <span class="text-primary font-medium">${this.formatDate(startDate)}</span>
            </div>
            ${this.generateDurationBadge(1)}
          </div>
          ${this.generateRequestInfo(requestDate)}
        </div>
        ${this.generateConfirmationMessage()}
      </div>
    `;

    const multiDayTemplate = `
      <div class="flex flex-column gap-3">
        <div class="surface-card p-2 border-round border-2 surface-border">
          <div class="flex justify-content-between align-items-center mb-3">
            <div class="flex align-items-center gap-2">
              <i class="pi pi-calendar-times text-primary"></i>
              <span class="text-600">Duration</span>
            </div>
            ${this.generateDurationBadge(request.dates.length)}
          </div>
          
          <div class="flex align-items-center justify-content-between gap-3 mb-3">
            ${this.generateDateDisplay('From', startDate)}
            <i class="pi pi-arrow-right text-500"></i>
            ${this.generateDateDisplay('To', endDate)}
          </div>

          ${this.generateRequestInfo(requestDate)}
        </div>
        ${this.generateConfirmationMessage()}
      </div>
    `;

    this.confirmationService.confirm({
      header: 'Remove Days Off Request',
      message: isSingleDay ? singleDayTemplate : multiDayTemplate,
      accept: () => {
        this.removeDaysOffRequest(request.requestId);
      }
    });
  }

  // Update the template to show admin actions for all statuses
  canAdminManageRequest(status: IDayOffRequestStatusEnum): boolean {
    return true; // Admin can manage all requests regardless of status
  }

  handleDaysOffRequest(request: any, action: 'approve' | 'reject'): void {
    const currentStatus = request.status;
    const statusEnum = action === 'approve' ?
      IDayOffRequestStatusEnum.Approved :
      IDayOffRequestStatusEnum.Rejected;

    let confirmMessage = `Are you sure you want to ${action} this days off request?`;

    // Add more context based on current status
    if (currentStatus === IDayOffRequestStatusEnum.Approved && action === 'reject') {
      confirmMessage = 'Are you sure you want to change this approved request to rejected?';
    } else if (currentStatus === IDayOffRequestStatusEnum.Rejected && action === 'approve') {
      confirmMessage = 'Are you sure you want to change this rejected request to approved?';
    }

    this.confirmationService.confirm({
      header: `${action.charAt(0).toUpperCase() + action.slice(1)} Days Off Request`,
      message: confirmMessage,
      accept: () => {
        this.apiService.getApiData<unknown>(
          { url: IAvailability.postProcessDayOff, method: 'POST' },
          {
            teacherId: this.userProfileInfo?.userId!,
            requestId: request.requestId,
            statusEnum: statusEnum
          } as IPostProcessDayOffRequest,
          true
        ).pipe(this.untilDestroyed()).subscribe({
          next: (res) => {
            console.log(`Days off request ${action}ed:`, res);
            this.toastService.show({
              severity: 'success',
              summary: 'Success',
              detail: `Days off request ${action}ed successfully`,
              position: 'top-right'
            });
            this.getDaysOff();
          },
          error: (err) => {
            console.error(`Failed to ${action} days off request:`, err);
            this.toastService.show({
              severity: 'error',
              summary: 'Error',
              detail: `Failed to ${action} days off request`,
              position: 'top-right'
            });
          }
        });
      }
    });
  }

  removeDaysOffRequest(requestId: string): void {

    this.apiService.getApiData<unknown>(
      { url: IAvailability.postRemoveDayOff, method: 'POST' },
      {
        teacherId: this.userProfileInfo?.userId!,
        requestId: requestId
      } as IPostRemoveDayOffRequest,
      true
    ).pipe(this.untilDestroyed()).subscribe({
      next: (res) => {
        console.log('Days off request removed:', res);
        this.getDaysOff();
      },
      error: (err) => {
        console.error('Failed to remove days off request:', err);
      },
    });
  }

  openDaysOffDialog(event: any): void {
    const params = { ...event, teacher: this.userProfileInfo! };
    this.generalService.openComponent(DaysOffDialogComponent, params);
  }

  goToRequestDate(date: Date): void {
    const calendarApi = this.calendarComponent.getApi();
    calendarApi.gotoDate(date);
  }

  // Status helper methods
  getStatusLabel(status: number): any {
    return this.STATUS_CONFIG[status]?.label || 'Unknown';
  }

  getStatusSeverity(status: number): any {
    return this.STATUS_CONFIG[status]?.severity || 'info';
  }

  getStatusColor(status: number): any {
    return this.STATUS_CONFIG[status]?.color || 'var(--p-tag-info-background)';
  }

  // Data helpers
  // Get counts for a specific year
  getDaysOffCounts(year: number) {
    const yearData = this.yearlyDaysOff().find(y => y.year === year);
    if (!yearData) return {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      remaining: 0,
      allowed: 0
    };

    const counts = yearData.requests.reduce((acc, request) => {
      // Only count days that fall within the specified year
      const daysCount = request.dates.filter((date: Date) =>
        date.getFullYear() === year
      ).length;

      switch (request.status) {
        case IDayOffRequestStatusEnum.Pending:
          acc.pending += daysCount;
          break;
        case IDayOffRequestStatusEnum.Approved:
          acc.approved += daysCount;
          break;
        case IDayOffRequestStatusEnum.Rejected:
          acc.rejected += daysCount;
          break;
      }
      return acc;
    }, {
      pending: 0,
      approved: 0,
      rejected: 0
    });

    // Total only includes approved and pending requests
    const total = counts.approved + counts.pending;
    const remaining = yearData.allowedDaysOffCount - total;
    return {
      ...counts,
      total,
      remaining,
      allowed: yearData.allowedDaysOffCount
    };
  }

  // Format counts for display
  getFormattedDaysOffCounts(year: number) {
    const counts = this.getDaysOffCounts(year);
    return {
      remaining: `${counts.remaining}/${counts.allowed}`,
      pending: `${counts.pending}`,
      approved: `${counts.approved}`,
      rejected: this.showRejected() ? `${counts.rejected}` : '0',
      total: `${counts.total}/${counts.allowed}`
    };
  }


  private transformDaysOffData(data: IGetDaysOffResponse): DayOffRequest[] {
    const yearlyData: YearlyDaysOff[] = [];
    const allRequests: YearlyDaysOff[] = [];
    const userTimeZone = this.userTimezone; // Capture user timezone

    data.daysOffByYear.forEach((yearData: IDayOffByYear) => {
      const requestGroups = new Map();

      yearData.dayOffs.forEach((dayOff: ITeacherDayOffDto) => {

        if (!this.showRejected() && dayOff.status === IDayOffRequestStatusEnum.Rejected) {
          return;
        }

        if (!requestGroups.has(dayOff.requestId)) {
          requestGroups.set(dayOff.requestId, {
            requestId: dayOff.requestId,
            dates: [],
            status: dayOff.status,
            // Convert these dates to user timezone when storing in the request object
            requestedOn: this.timezoneService.convertToLocalTime(dayOff.requestedOn, userTimeZone),
            rejectedOn: dayOff.rejectedOn ? this.timezoneService.convertToLocalTime(dayOff.rejectedOn, userTimeZone) : undefined,
            approvedOn: dayOff.approvedOn ? this.timezoneService.convertToLocalTime(dayOff.approvedOn, userTimeZone) : undefined,
            year: yearData.year
          });
        }
        // Convert individual dayOff.date to user timezone before pushing
        requestGroups.get(dayOff.requestId).dates.push(this.timezoneService.convertToLocalTime(dayOff.date, userTimeZone));
      });

      const yearRequests = Array.from(requestGroups.values());

      yearlyData.push({
        year: yearData.year,
        allowedDaysOffCount: yearData.allowedDaysOffCount,
        requests: yearRequests
      });

      allRequests.push(...yearRequests);
    });

    this.yearlyDaysOff.set(yearlyData);
    return allRequests.flatMap(yearData => yearData.requests) as DayOffRequest[];
  }

  private createCalendarEvents(data: IGetDaysOffResponse): CalendarEvent[] {
    const events: CalendarEvent[] = [];
    const userTimeZone = this.userTimezone;

    (data as IGetDaysOffResponse).daysOffByYear.forEach((yearData: IDayOffByYear) => {
      const requestGroups = new Map();

      const filteredDayOffs = yearData.dayOffs.filter((dayOff: ITeacherDayOffDto) =>
        this.showRejected() || dayOff.status !== IDayOffRequestStatusEnum.Rejected
      );

      filteredDayOffs.forEach((dayOff: ITeacherDayOffDto) => {
        if (!requestGroups.has(dayOff.requestId)) {
          requestGroups.set(dayOff.requestId, {
            dates: [],
            status: dayOff.status,
            requestId: dayOff.requestId,
            // Use TimezoneService for date conversions
            requestedOn: this.timezoneService.convertToLocalTime(dayOff.requestedOn, userTimeZone),
            rejectedOn: dayOff.rejectedOn ? this.timezoneService.convertToLocalTime(dayOff.rejectedOn, userTimeZone) : null,
            approvedOn: dayOff.approvedOn ? this.timezoneService.convertToLocalTime(dayOff.approvedOn, userTimeZone) : null,
          });
        }
        // Convert date using TimezoneService
        requestGroups.get(dayOff.requestId).dates.push(
          this.timezoneService.convertToLocalTime(dayOff.date, userTimeZone)
        );
      });

      requestGroups.forEach((request, requestId) => {
        if (request.dates.length > 0) {
          // Use TimezoneService for end date calculation
          const lastDate = request.dates[request.dates.length - 1];
          const endDate = this.timezoneService.addDuration(lastDate, 1, 'days', userTimeZone);

          events.push({
            title: this.getStatusLabel(request.status),
            start: request.dates[0],
            end: endDate,
            color: this.getStatusColor(request.status),
            extendedProps: {
              request: request,
              requestId: requestId,
              status: request.status,
              requestedOn: request.requestedOn,
              rejectedOn: request.rejectedOn,
              approvedOn: request.approvedOn,
              daysCount: request.dates.length,
              statusLabel: this.getStatusLabel(request.status)
            }
          });
        }
      });
    });

    return events;
  }

  private isEarliestDayAfterMinimumNotice(
    requestedDays: Date[] | null | undefined,
    isAdmin: boolean,
    timeZoneIana: string
  ): boolean {
    // Constants
    const MIN_ADVANCE_NOTICE_DAYS = 14;
  
    // Return false if no days are requested
    if (!requestedDays || requestedDays.length === 0) {
      return false;
    }
  
    // Find earliest requested day
    const earliestDayOff = moment(
      requestedDays.reduce((earliest, current) => 
        current < earliest ? current : earliest
      )
    ).startOf('day');
  
    // Get current date in user's timezone
    const userLocalToday = moment().tz(timeZoneIana).startOf('day');
    // Calculate minimum allowed date based on user type
    const minAllowedDate = isAdmin
      ? userLocalToday
      : moment(userLocalToday).add(MIN_ADVANCE_NOTICE_DAYS, 'days');
  
    // Calculate and alert the days difference
    const daysDifference = earliestDayOff.diff(userLocalToday, 'days');
    
    // Check if earliest day is on or after minimum allowed date
    return earliestDayOff.isSameOrAfter(minAllowedDate);
  }
}