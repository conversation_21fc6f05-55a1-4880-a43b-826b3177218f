import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable, Signal, signal } from '@angular/core';
import { catchError, delay, Observable, tap, throwError } from 'rxjs';
import { Events } from './event-bus.service';
import { ToastService } from './toast.service';
import { getToastMessage, ToastMessages } from '../models/toast-messages';
import { IGetAllTeachingLanguagesResponse, IGetBasketResponse, IGetCountriesResponse, IGetDialCodesResponse, IGetLanguagesResponse, IGetProfileInfoResponse, IGetStudentDashboardResponse, IGetStudentGroupResponse, IGetTeachingLanguageResponse, IGetTimezonesResponse, IParentDashboardHandlerResponse, IpGeoResponse, IStudentGroupDto } from '../GeneratedTsFiles';


export type DataT<T> = T; // Consider using PascalCase for type names

export interface State<T> {
  loading: boolean;
  data: DataT<T> | any | null; 
  error: string | null;
  initialized: boolean;
  hasError: boolean;
  errorStatusCode: number | null;
}

@Injectable({
  providedIn: 'root'
})
export class DataApiStateService<T> {

  readonly parentStudents = this.createStateSignal<T | null | undefined>();
  readonly parentStudentsGroups = this.createStateSignal<IStudentGroupDto[]>();
  readonly teachingLanguages = this.createStateSignal<IGetAllTeachingLanguagesResponse>();
  readonly startImpersonateStudent = this.createStateSignal<any | null | undefined>();
  readonly createStudentGroup = this.createStateSignal<IGetStudentGroupResponse>();
  readonly getBasket = this.createStateSignal<IGetBasketResponse>();
  readonly getGeoLocationData = this.createStateSignal<T | IpGeoResponse | null | undefined>();
  readonly getCountries = this.createStateSignal<IGetCountriesResponse>();
  readonly getTimezones = this.createStateSignal<IGetTimezonesResponse>();
  readonly getDialCodes = this.createStateSignal<IGetDialCodesResponse>();
  readonly getParentDashboard = this.createStateSignal<IParentDashboardHandlerResponse>();
  readonly getStudentDashboard = this.createStateSignal<IGetStudentDashboardResponse>();
  readonly getNativeLanguages = this.createStateSignal<IGetLanguagesResponse>();
  readonly getUserProfileInfo = this.createStateSignal<IGetProfileInfoResponse>();

  readonly toastService = inject(ToastService);

  createStateSignal<T>(): { state: Signal<State<T>>, setState: (state: State<T>) => void } {
    const stateSignal = signal<State<T>>({
      loading: false,
      data: null,
      error: null,
      errorStatusCode: null,
      initialized: false,
      hasError: false,
    });
    return {
      state: stateSignal,
      setState: (state: State<T>) => stateSignal.set(state)
    };
  }

  handleApiCall<T>(
    apiCall: Observable<T>,
    setState: (state: State<T>) => void,
    delayMs: number = 0, // Default delay
    showToast = true,
  ): Observable<T> {
    this.setLoadingState(setState);

    return apiCall.pipe(
      delay(delayMs), // Introduce delay before emitting the response
      tap(res => this.handleApiResponse(res, setState)),
      catchError(err => this.handleApiError(err, setState, showToast))
    );
  }

  // Set loading state
  private setLoadingState<T>(setState: (state: State<T>) => void): void {
    setState({
      loading: true,
      data: null,
      error: null,
      errorStatusCode: null,
      initialized: true,
      hasError: false,
    });
  }

  // Handle successful API response
  private handleApiResponse<T>(res: T, setState: (state: State<T>) => void): void {
    const isEmpty = Array.isArray(res) ? res.length === 0 : !res;

    setState({
      loading: false,
      data: isEmpty ? null : res, // Set data to null if response is empty
      error: null,
      errorStatusCode: null,
      initialized: true,
      hasError: false,
    });
  }

  // Handle API error
  private handleApiError<T>(err: any, setState: (state: State<T>) => void, showToast: boolean): Observable<never> {
    const errorResponse = this.createErrorResponse(err, showToast);
    const errorMessage = this.getErrorMessage(errorResponse);
    console.error(errorMessage); // Log the error
    setState({
      loading: false,
      data: null,
      error: errorMessage || 'Error loading data',
      errorStatusCode: errorResponse.status,
      initialized: true,
      hasError: true,
    });

    return throwError(() => errorResponse); // Rethrow the error
  }

  // Create a standardized error response
  private createErrorResponse(err: any, showToast: boolean): HttpErrorResponse {
    if (err instanceof HttpErrorResponse) {
      return err;
    }

    console.log(err);
    if (err && err.messages && showToast) {
      const errorMessage = err.messages.join(', ');
      // TODO: handle specific error messages
      // this.generalService.setErrorDataSignal(errorMessage);
      this.toastService.show(getToastMessage(ToastMessages.StateApiMessage.error, { data: errorMessage }));
    }

    if (!err) {
      return new HttpErrorResponse({
        error: 'Unknown Error',
        status: 0,
        statusText: 'Unknown Error',
        url: '',
      });
    }

    return new HttpErrorResponse({
      error: err.error,
      status: err.status || 0,
      statusText: err.statusText || 'Unknown Error',
      url: err.url || '',
    });
  }

  private getErrorMessage(error: HttpErrorResponse): string | undefined {
    switch (error.status) {
      case 400:
        return 'Bad Request. Please check the input and try again.';
      case 401:
        return undefined;
      case 403:
        return 'Forbidden. You do not have permission to perform this action.';
      case 404:
        return 'Not Found. The requested resource could not be found.';
      case 500:
        return 'Internal Server Error. Please try again later.';
      default:
        return `Unexpected error: ${error.message}`;
    }
  }

  getEventNameForSetState(setState: (state: State<unknown>) => void): string {
    const eventMap: { [key: string]: Events } = {
      [this.getBasket.setState.name]: Events.StateLoadGetBasket,
      [this.getCountries.setState.name]: Events.StateLoadCountries,
      [this.getTimezones.setState.name]: Events.StateLoadTimezones,
      [this.getDialCodes.setState.name]: Events.StateLoadDialCodes,
      [this.getGeoLocationData.setState.name]: Events.StateLoadGeoLocationData,
      [this.parentStudents.setState.name]: Events.StateLoadParentStudents,
      [this.parentStudentsGroups.setState.name]: Events.StateLoadParentStudentsGroups,
      [this.startImpersonateStudent.setState.name]: Events.StateLoadStartImpersonate,
      [this.teachingLanguages.setState.name]: Events.StateLoadTeachingLanguages,
      [this.getParentDashboard.setState.name]: Events.StateLoadParentDashboard,
      [this.getStudentDashboard.setState.name]: Events.StateLoadStudentDashboard,
      [this.getUserProfileInfo.setState.name]: Events.StateLoadProfileInfo,
    };

    return (eventMap[setState.name] ?? {} as Events).toString();
  }

  // Helper to get current state (since signals are read-only outside)
  private getCurrentState<T>(setState: (state: State<T>) => void): State<T> {
    const stateMap = new Map([
      [this.parentStudents.setState, this.parentStudents.state()],
      [this.parentStudentsGroups.setState, this.parentStudentsGroups.state()],
      [this.teachingLanguages.setState, this.teachingLanguages.state()],
      [this.startImpersonateStudent.setState, this.startImpersonateStudent.state()],
      [this.createStudentGroup.setState, this.createStudentGroup.state()],
      [this.getBasket.setState, this.getBasket.state()],
      [this.getGeoLocationData.setState, this.getGeoLocationData.state()],
      [this.getCountries.setState, this.getCountries.state()],
      [this.getTimezones.setState, this.getTimezones.state()],
      [this.getDialCodes.setState, this.getDialCodes.state()],
      [this.getParentDashboard.setState, this.getParentDashboard.state()],
      [this.getStudentDashboard.setState, this.getStudentDashboard.state()],
      [this.getNativeLanguages.setState, this.getNativeLanguages.state()],
      [this.getUserProfileInfo.setState, this.getUserProfileInfo.state()],
    ]);

    return stateMap.get(setState) || {
      loading: false,
      data: null,
      error: null,
      errorStatusCode: null,
      initialized: false,
      hasError: false,
    };
  }

  // New method to set state directly
  setStateDirectly<T>(setState: (state: State<T>) => void, newState: Partial<State<T>>): void {
    const currentState = this.getCurrentState(setState);
    const updatedState: State<T> = {
      ...currentState,
      ...newState,
      initialized: true, // Ensure initialized is true when setting state directly
    };
    setState(updatedState);
  }
  clearStateDirectly<T>(setState: (state: State<T>) => void): void {
    setState({
      loading: false,
      data: null,
      error: null,
      errorStatusCode: null,
      initialized: false,
      hasError: false,
    });
  }

}
