import { Injectable, inject } from "@angular/core";
import { HttpClient, HttpErrorResponse, HttpEvent, HttpEventType, HttpHeaders, HttpParams, HttpRequest, HttpResponse } from "@angular/common/http";
import { Observable, Observer, ReplaySubject, Subscription, catchError, filter, map, of, shareReplay, tap, throwError } from "rxjs";
import { EnvironmentService } from "./environment-config.service";
import { ToastService } from "../services/toast.service";
import { getToastMessage, ToastMessages } from "../models/toast-messages";
import { IApiResponse, IApiResponseBase, IAvailability, IdentityRoutes, IDialCodeDataDto, IEditAvailabilityRequest, IEditAvailabilityResponse, IFindCommonTimeSlotsRequest, IFindCommonTimeSlotsResponse, IGetAllTeachingLanguagesResponse, IGetAvailabilityRequest, IGetAvailabilityResponse, IGetCountriesResponse, IGetLanguagesRequest, IGetLanguagesResponse, IGetStudentDashboardRequest, IGetStudentDashboardResponse, IGetStudentsRequest, IGetStudentsResponse, IGetWhereDidYouHearAboutUsResponse, ImpersonateStudentRequest, IpGeolocation, IpGeoRequest, IpGeoResponse, IProfileInfo, IStopImpersonateStudentRequest, IStudents, ITimezoneData, LocationDataRoutes, TeachingLanguagesRoutes } from "../GeneratedTsFiles";
import { GeneralService } from "./general.service";

export const GEO_LOCATION_CONFIG = {
  "status": true,
  "message": false,
  "continent": false,
  "continentCode": false,
  "country": true,
  "countryCode": true,
  "region": true,
  "regionName": true,
  "city": true,
  "district": true,
  "zip": false,
  "lat": false,
  "lon": false,
  "timezone": true,
  "offset": true,
  "currency": false,
  "isp": false,
  "org": false,
  "as": false,
  "asname": false,
  "reverse": false,
  "mobile": false,
  "proxy": false,
  "hosting": false,
  "query": true
};

const ORIGIN = 'http://localhost:4200';
const IP_COUNTRY_API = "https://ipapi.co/json/";

interface IpApiResponse {
  country_code?: string;
  country?: string;
  city?: string;
  region?: string;
  error?: boolean;
  reason?: string;
}

type ResponseMeta = {
  status: number;
  statusText?: string;
  headers?: HttpHeaders;
  url?: string | null;
};

type WithMeta<T> = T & { __meta?: ResponseMeta };

export enum ApiEndpoint {
  GetCountries,
  GetTimezones,
  GetDialCodes,
  GetLanguages,
  GetTeachingLanguages,
  GetWhereDidYouHearAboutUs,
  TestSetStudentTeachingLanguageLevel
}

const endpointMap = {
  [ApiEndpoint.GetCountries]: { url: LocationDataRoutes.getCountries, method: 'GET' },
  [ApiEndpoint.GetTimezones]: { url: LocationDataRoutes.getTimezones, method: 'GET' },
  [ApiEndpoint.GetDialCodes]: { url: LocationDataRoutes.getDialCodes, method: 'GET' },
  [ApiEndpoint.GetLanguages]: { url: LocationDataRoutes.getLanguages, method: 'GET' },
  [ApiEndpoint.GetTeachingLanguages]: { url: TeachingLanguagesRoutes.getAllTeachingLanguages, method: 'GET' },
  [ApiEndpoint.GetWhereDidYouHearAboutUs]: { url: LocationDataRoutes.getWhereDidYouHearAboutUs, method: 'GET' },
  [ApiEndpoint.TestSetStudentTeachingLanguageLevel]: { url: "SetStudentTeachingLanguageLevel", method: 'POST' },
};

@Injectable({ providedIn: 'root' })
export class HandleApiResponseService {
  private http = inject(HttpClient);
  private environmentService = inject(EnvironmentService);
  private endpointUrl = this.environmentService.apiUrl;
  private toastService = inject(ToastService);
  private generalService = inject(GeneralService);
  private cacheMap = new Map<string, Observable<unknown>>();

  constructor() { }

  private handleApiRequest<T>(endpoint: { url: string, method: string, responseType?: 'json' | 'blob' },
    payload: Record<string, unknown> | undefined, showToastError: boolean): Observable<T> {
    const { url, method, responseType } = endpoint;

    // Define the observable creation logic
    const isGetMethod = method === 'GET';
    const apiCall$ = isGetMethod
      ? this.customHandleApiResponse<T>(
        url,
        'GET',
        null,           // No body data for GET requests
        undefined,      // Use default headers
        payload,        // Query parameters
        responseType
      )
      : this.customHandleApiResponse<T>(
        url,
        method as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
        payload,        // Request body
        undefined,      // Use default headers
        undefined,      // No query parameters
        responseType
      );

    return apiCall$;
  }

  getApiData<T>(endpoint: { url: string, method: string, responseType?: 'json' | 'blob' }, params?: Record<string, unknown> | null, showToastError = true): Observable<T> {
    return this.handleApiRequest<T>(endpoint, params || undefined, showToastError).pipe(
      catchError((error: HttpErrorResponse | IApiResponseBase) => {
        console.log("🚀 ~ HandleApiResponseService ~ catchError ~ error:", error);

        // Determine if it's HttpErrorResponse or IApiResponseBase
        const apiError = error instanceof HttpErrorResponse
          ? this.extractApiErrorFromHttpError(error)
          : error as IApiResponseBase;

        if (showToastError) {
          this.showErrorToast(apiError, endpoint.url);
        }

        return throwError(() => apiError);
      })
    );
  }


  private extractApiErrorFromHttpError(httpError: HttpErrorResponse): IApiResponseBase {
    // Extract API error from HttpErrorResponse
    const errorBody = httpError.error;

    return {
      statusCode: httpError.status,
      messages: errorBody?.messages || [],
      formValidationErrors: errorBody?.formValidationErrors || null,
    };
  }

  private showErrorToast(apiError: IApiResponseBase, endpointUrl: string): void {
    // Combine messages and formValidationErrors into a single string
    const messageArray = Array.isArray(apiError.messages) ? apiError.messages : [];

    // Extract validation errors from all form fields
    const formErrorsArray: string[] = [];
    if (apiError.formValidationErrors) {
      Object.entries(apiError.formValidationErrors).forEach(([field, errors]) => {
        const errorList = Array.isArray(errors) ? errors : [errors];
        errorList.forEach(error => {
          // Format field name for better readability
          const formattedField = field
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
            .trim();

          formErrorsArray.push(`${formattedField}: ${error}`);
        });
      });
    }

    // Combine all errors and filter out empty values
    const allErrors = [...messageArray, ...formErrorsArray].filter(Boolean);
    const errorMessages = allErrors.length > 0 ? allErrors.join(', ') : 'An unknown error occurred';

    this.toastService.show(
      getToastMessage(ToastMessages.StateApiMessage.error, {
        eventName: endpointUrl,
        data: (apiError.statusCode ? (`StatusCode: ${apiError.statusCode}`) : '') + ' Error: ' + errorMessages
      })
    );
  }

  /**
 * Handles multipart/form-data requests with progress tracking
 * @param url The API endpoint URL
 * @param formData The FormData object containing files and data
 * @param method HTTP method (POST or PUT), defaults to POST
 * @param headers Optional custom headers
 * @returns Observable of HttpEvent<T> including progress events and response
 */
  sendMultipartFormData<T>(
    url: string,
    formData: FormData,
    method: 'POST' | 'PUT' | 'PATCH' = 'POST',
    headers?: HttpHeaders,
    showToastError = true
  ): Observable<{ progress: number; response?: HttpResponse<T> }> {
    headers = headers
      ? headers.append('Client-Domain', ORIGIN)
      : new HttpHeaders({ 'Client-Domain': ORIGIN });

    return new Observable<{ progress: number; response?: HttpResponse<T> }>((observer: Observer<{ progress: number; response?: HttpResponse<T> }>) => {
      let lastProgress = 0; // Track last reported progress to avoid redundant updates

      this.http
        .request<T>(method, `${this.endpointUrl}/${url}`, {
          body: formData,
          headers,
          reportProgress: true, // Enables upload progress tracking
          observe: 'events', // Ensures we receive upload progress events
        })
        .subscribe({
          next: (event: HttpEvent<T>) => {
            if (event.type === HttpEventType.UploadProgress && event.total) {
              // Calculate upload progress percentage (0-90% range to leave room for response)
              const uploadProgress = Math.min(90, Math.round((100 * event.loaded) / event.total));
              if (uploadProgress !== lastProgress) { // Only emit if progress changes
                console.log(`Upload progress: ${uploadProgress}%`);
                this.generalService.setUploadingProgress(uploadProgress);
                observer.next({ progress: uploadProgress });
                lastProgress = uploadProgress;
              }
            } else if (event.type === HttpEventType.ResponseHeader) {
              // Response headers received, indicate nearing completion (e.g., 95%)
              if (lastProgress < 95) {
                console.log('Response headers received, nearing completion');
                this.generalService.setUploadingProgress(95);
                observer.next({ progress: 95 });
                lastProgress = 95;
              }
            } else if (event instanceof HttpResponse) {
              // Full response received, mark as 100%
              console.log('Upload and response complete');
              this.generalService.setUploadingProgress(100);
              observer.next({ progress: 100, response: event });
              observer.complete();
              if (event.status !== 200) {
                observer.error(event.body);
              }
            }
          },
          error: (errorResponse: HttpErrorResponse) => {
            console.error('Multipart API Error:', errorResponse);
            const errorBody = errorResponse.error;

            if (errorBody?.statusCode === 400 && (errorBody.errors || errorBody.formValidationErrors)) {
              const formattedErrors: string[] = [];

              // Handle both 'errors' and 'formValidationErrors'
              const errorSources = errorBody.errors || errorBody.formValidationErrors || {};

              Object.entries(errorSources).forEach(([field, messages]: [string, any]) => {
                // Ensure messages is an array
                const errorMessages = Array.isArray(messages) ? messages : [messages];

                errorMessages.forEach((message: string) => {
                  const match = message.match(/'([^']+)'/);
                  if (match && match[1]) {
                    const messageParts = message.split("'");
                    const friendlyMessage = `${match[1]} ${messageParts[messageParts.length - 1].trim()}`;
                    formattedErrors.push(friendlyMessage);
                  } else {
                    // Use the field name to make the message more readable if no quotes are present
                    const formattedField = field
                      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
                      .trim();
                    formattedErrors.push(`${formattedField}: ${message}`);
                  }
                });
              });

              const apiError: Partial<IApiResponseBase> = {
                statusCode: errorBody.statusCode,
                messages: formattedErrors,
                formValidationErrors: errorBody.formValidationErrors || errorBody.errors,
              };

              if (showToastError) {
                this.toastService.show(
                  getToastMessage(ToastMessages.StateApiMessage.error, {
                    eventName: url,
                    data: formattedErrors.join("\n"),
                  })
                );
              }

              observer.error(apiError);
            } else {
              observer.error(this.handleErrorResponse(errorBody, "status"));
            }
          },
        });
    });
  }

  customHandleApiResponse<T>(
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    data?: unknown,
    headers?: HttpHeaders,
    params?: unknown,
    responseType: 'json' | 'blob' = 'json'
  ): Observable<T> {
    headers = headers ? headers.append('Client-Domain', ORIGIN) : new HttpHeaders({ 'Client-Domain': ORIGIN });
    let httpParams = new HttpParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => httpParams = httpParams.append(key, String(v)));
        } else {
          httpParams = httpParams.append(key, String(value));
        }
      });
    }

    const request = new HttpRequest<unknown>(
      method,
      `${this.endpointUrl}/${url}`,
      data,
      { headers, params: httpParams, reportProgress: true, responseType }
    );

    const apiCall$ = this.http.request<IApiResponse<T>>(request).pipe(
      tap((event) => {
        if (event.type === HttpEventType.UploadProgress && event.total) {
          const progress = Math.round((100 * event.loaded) / event.total);
          console.log(`Upload progress: ${progress}%`);
          this.generalService.setUploadingProgress(progress);
        }
      }),
      filter((event): event is HttpResponse<IApiResponse<T>> => event instanceof HttpResponse),
      map((response: HttpResponse<IApiResponse<T>>): T => {
        if (this.isSuccessStatusCode(response.status)) {
          console.log('API Response:', response);
          const rawData = responseType === 'json' ? response.body?.data : response as unknown as T;

          if (rawData !== null && rawData !== undefined) {
            return {
              ...(rawData as object),
              __meta: {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                url: response.url
              }
            } as WithMeta<T>;
          }

          return rawData as T;
        }
        throw new Error(`Request failed with status code: ${response.status}`);
      }),
      catchError((errorResponse: HttpErrorResponse) => {
        console.error('API Error:', errorResponse);
        
        // return 404 - 401 - 403 without a ui message
        if (errorResponse.status === 404 || errorResponse.status === 401 || errorResponse.status === 403) {
          return of(errorResponse.error?.body);
        }
        // Extract the error data from the response
        const errorData = errorResponse.error;
        
        // Process the error and show toast notification
        const apiError = this.extractApiErrorFromHttpError(errorResponse);
        this.showErrorToast(apiError, url);
        this.generalService.hideDivLoading();
        
        return throwError(() => this.handleErrorResponse(errorData));
      })
    );

    return apiCall$;
  }

  private handleErrorResponse(error: IApiResponseBase, statusAttribute = 'statusCode'): IApiResponseBase {
    if (!error) {
      console.error('An error occurred with no details');
      return {
        statusCode: 500,
        messages: ['An error occurred. Please try again later.'],
        formValidationErrors: undefined,
      };
    }

    // Extract error information
    const statusCode = (statusAttribute === 'statusCode' ? error.statusCode : null) || 500;
    const messages = error.messages || [];
    const formValidationErrors = error.formValidationErrors || {};

    // Log appropriate error based on status code
    switch (statusCode) {
      case 401:
        console.error('Unauthorized:', messages);
        break;
      case 404:
        console.error('Not Found:', messages);
        break;
      default:
        console.error(`Error (${statusCode}):`, messages);
        break;
    }

    // Log form validation errors if present
    if (formValidationErrors && Object.keys(formValidationErrors).length > 0) {
      console.error('Form Validation Errors:', formValidationErrors);
    }

    // Format all error messages for display
    const formattedError: IApiResponseBase = {
      statusCode,
      messages: Array.isArray(messages) ? messages : [],
      formValidationErrors,
    };

    return formattedError;
  }


  private isSuccessStatusCode(statusCode: number): boolean {
    return statusCode >= 200 && statusCode <= 299;
  }

  // Method to clear cache for a specific endpoint
  private clearCache(cacheKey: string): void {
    if (this.cacheMap.has(cacheKey)) {
      this.cacheMap.delete(cacheKey);
      console.log(`Cache cleared for ${cacheKey}`);
    }
  }

  // Method to clear all caches
  clearAllCaches(): void {
    this.cacheMap.clear();
    console.log('All caches cleared');
  }

  clearProfilePhotoCache(userId: string): void {
    // const url = IProfileInfo.getProfilePhoto.replace('{UserId}', userId);
    // const cacheKey = `GET:${url}:blob`;
    // this.clearCache(cacheKey);
  }

  subscribeGeneric<T>(
    observable: Observable<T>,
    nextCallback: (value: T) => void,
    errorCallback: (error: Error | HttpErrorResponse | IApiResponseBase) => void
  ): Subscription {
    return observable.subscribe({ next: nextCallback, error: errorCallback });
  }

  getCountries(): Observable<IGetCountriesResponse | unknown> {
    return this.customHandleApiResponse<IGetCountriesResponse>(LocationDataRoutes.getCountries, 'GET');
  }

  getTimezones(): Observable<{ timezonesData: ITimezoneData[]; } | undefined> {
    return this.customHandleApiResponse<{ timezonesData: ITimezoneData[]; }>(LocationDataRoutes.getTimezones, 'GET');
  }

  getDialCodes(): Observable<IDialCodeDataDto | unknown> {
    return this.customHandleApiResponse<IDialCodeDataDto>(LocationDataRoutes.getDialCodes, 'GET');
  }

  getLanguages(): Observable<IGetLanguagesRequest | unknown> {
    return this.customHandleApiResponse<IGetLanguagesResponse>(LocationDataRoutes.getLanguages, 'GET');
  }

  getTeachingLanguages(): Observable<IGetAllTeachingLanguagesResponse> {
    return this.customHandleApiResponse<IGetAllTeachingLanguagesResponse>(TeachingLanguagesRoutes.getAllTeachingLanguages, 'GET');
  }

  getWhereDidYouHearAboutUs(): Observable<IGetWhereDidYouHearAboutUsResponse> {
    return this.customHandleApiResponse<IGetWhereDidYouHearAboutUsResponse>(LocationDataRoutes.getWhereDidYouHearAboutUs, 'GET');
  }

  getCountryCode(): Observable<string> {
    return this.http.get<IpApiResponse>(IP_COUNTRY_API).pipe(
      map(response => {
        if (response && response.country_code) {
          return response.country_code;
        } else {
          throw new Error('Country code not found in response');
        }
      })
    );
  }

  impersonateStudent(data: ImpersonateStudentRequest): Observable<ImpersonateStudentRequest> {
    return this.customHandleApiResponse<ImpersonateStudentRequest>(IdentityRoutes.postImpersonateStudent, 'POST', data);
  }

  stopImpersonateStudent(data: IStopImpersonateStudentRequest): Observable<IStopImpersonateStudentRequest> {
    return this.customHandleApiResponse<IStopImpersonateStudentRequest>(IdentityRoutes.postStopImpersonateStudent, 'POST', data);
  }

  getParentStudents(data: IGetStudentsRequest): Observable<IGetStudentsResponse> {
    return this.customHandleApiResponse<IGetStudentsResponse>(IStudents.getStudents, 'GET', null, undefined, data);
  }

  getIpGeoLocationData(data?: IpGeoRequest): Observable<IpGeoResponse> {
    return this.customHandleApiResponse<IpGeoResponse>(IpGeolocation.post_IpGeolocation, 'POST', GEO_LOCATION_CONFIG);
  }

  getAvailability(data: IGetAvailabilityRequest): Observable<IGetAvailabilityResponse> {
    return this.customHandleApiResponse<IGetAvailabilityResponse>(IAvailability.getAvailability, 'GET', null, undefined, data);
  }

  createAvailability(data: IGetAvailabilityRequest): Observable<IGetAvailabilityResponse> {
    return this.customHandleApiResponse<IGetAvailabilityResponse>(IAvailability.postCreateAvailability, 'POST', data);
  }

  editAvailability(data: IEditAvailabilityRequest): Observable<IEditAvailabilityResponse> {
    return this.customHandleApiResponse<IEditAvailabilityResponse>(IAvailability.patchEditAvailability, 'PATCH', data);
  }

  findCommonTimeSlots(data: IFindCommonTimeSlotsRequest): Observable<IFindCommonTimeSlotsResponse> {
    return this.customHandleApiResponse<IFindCommonTimeSlotsResponse>(IAvailability.postFindCommonTimeSlots, 'POST', data);
  }
}
