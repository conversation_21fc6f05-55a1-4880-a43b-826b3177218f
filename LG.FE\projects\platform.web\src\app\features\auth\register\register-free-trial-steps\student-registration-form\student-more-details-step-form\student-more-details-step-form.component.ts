import { CommonModule } from '@angular/common';
import { Component, inject, signal, OnInit, ChangeDetectionStrategy } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { TextareaModule } from 'primeng/textarea';
import { ButtonModule } from 'primeng/button';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ProgressBarModule } from 'primeng/progressbar';

// Importing from 'SharedModules.Library' assumes these are publicly exposed modules/types
import {
  ApiLoadingStateService,
  AuthStateService,
  HandleApiResponseService,
  IRegisterStudentRequest,
  IRegisterStudentResponse,
  PrimeCountriesDropdownComponent,
  PrimeTimezoneDropdownComponent,
  Severity,
  ToastService,
  IApiResponseBase,
  ICountry,
  IdentityRoutes,
  ITimezoneData,
  FormFieldValidationMessageComponent,
  PrimeReactiveFormInputComponent,
  EmitEvent,
  EventBusService,
  GeneralService,
  Events,
  getToastMessage, // Assuming this is also from SharedModules.Library or similar helpers
  ToastMessages // Assuming this is from SharedModules.Library or similar helpers
} from 'SharedModules.Library';

import { RegisterService } from '@platform.app/core/services/register.service';
import { UserService } from '@platform.app/core/services/user.service';
import { CustomValidators } from '@platform.app/core/helpers/custom-validators';
import { untilDestroyed } from 'SharedModules.Library'; // Assuming this is the custom helper


@Component({
  selector: 'app-student-more-details-step-form',
  standalone: true, // Mark as standalone component
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DropdownModule,
    ButtonModule,
    TextareaModule,
    ProgressSpinnerModule,
    ProgressBarModule,
    FormFieldValidationMessageComponent,
    PrimeReactiveFormInputComponent,
    PrimeCountriesDropdownComponent,
    PrimeTimezoneDropdownComponent,
  ],
  templateUrl: './student-more-details-step-form.component.html',
  styleUrl: './student-more-details-step-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush // Optimize change detection
})
export class StudentMoreDetailsStepFormComponent implements OnInit {

  // Injected Services
  private userService = inject(UserService);
  private registerService = inject(RegisterService);
  private apiService = inject(HandleApiResponseService);
  public generalService = inject(GeneralService); // Public for template use
  private eventBusService = inject(EventBusService);
  private authService = inject(AuthStateService);
  public apiLoadingStateService = inject(ApiLoadingStateService); // Public for template use
  private toastService = inject(ToastService);
  private formBuilder = inject(FormBuilder); // Inject FormBuilder directly

  // Component Properties
  public labelClass: string = 'block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full';
  public Severity = Severity; // Expose Severity enum for template
  public timezones = signal<ITimezoneData[]>([]); // Not used in provided snippet, but kept if for future expansion
  public loadingTimezones = signal<boolean>(true); // Not used in provided snippet, but kept if for future expansion
  public form!: FormGroup; // Initialized in ngOnInit
  public countries!: ICountry[]; // Not directly used in provided snippet, but kept if for future expansion
  public selectedCountry = signal<ICountry>({} as ICountry);
  public selectedTimezoneSignal = signal<ITimezoneData>({} as ITimezoneData);

  private untilDestroyed = untilDestroyed(); // Custom RxJS operator for unsubscription

  ngOnInit(): void {
    this.initializeForm();
  }

  /**
   * Marks all form controls as dirty and touched to trigger validation messages.
   * Useful before form submission.
   */
  public markFormAsDirty(): void {
    const markControls = (controls: { [key: string]: AbstractControl }) => {
      Object.values(controls).forEach(control => {
        if (control instanceof FormGroup) {
          markControls(control.controls);
        } else {
          control.markAsTouched();
          control.markAsDirty();
          // console.log(control.errors); // For debugging validation errors, remove in production
        }
      });
    };
    markControls(this.form.controls);
  }

  /**
   * Handles error responses from the student registration API call.
   * @param error The API response error object.
   */
  private handleRegisterStudentError = (error: IApiResponseBase): void => {
    console.error('Registration Error:', error);
    // Use the toast service for user-friendly error messages
    if (error.messages && error.messages.length > 0) {
      this.toastService.showError(error.messages.join(', '), 'Registration Failed');
    } else {
      this.toastService.showError(
        getToastMessage(ToastMessages.StateApiMessage.error, { data: 'An unexpected error occurred during registration.' }).detail,
        'Registration Failed'
      );
    }
    this.generalService.handleRouteError(error, IdentityRoutes.postRegisterStudent);
  };

  /**
   * Handles the country selection change event from the dropdown.
   * Updates the form's country value and sets the default timezone based on the selected country.
   * Also resets the state field if it's no longer required.
   * @param country The newly selected ICountry object.
   */
  onCountryChange(country: ICountry): void {
    const selectedCountry = country;
    this.setSelectedCountry(selectedCountry as ICountry);
    this.form.get('addressDto')!.get('country')!.setValue(selectedCountry);

    const timezoneValue = selectedCountry!.timezones![0];
    this.form.get('timeZoneIana')!.setValue(timezoneValue);
    this.form.get('addressDto')!.get('state')!.setValue(null);
    this.onTimezoneChange(timezoneValue);
    console.log(this.form.value);
  }

  /**
   * Handles the timezone selection change event from the dropdown.
   * Updates the selected timezone signal and the registration service request.
   * @param event The selected ITimezoneData object.
   */
  public onTimezoneChange(event: ITimezoneData): void {
    this.selectedTimezoneSignal.set(event);
    this.registerService.updateRegisterStudentRequest({
      ...this.registerService.getRegisterStudentRequest(),
      timeZoneIana: event.timezoneValue, // Assuming event provides a `timezoneValue` property
    });
  }

  /**
   * Determines if the 'state' field should be required based on the selected country.
   * @returns True if state is required, false otherwise.
   */
  shouldSetAState() {
    const countryValue = this.form.get('country')?.value;
    return countryValue ? (countryValue.name && this.generalService.isStateRequired(countryValue.name)) : false;
  }

  /**
   * Submits the form. If valid, prepares the registration request and
   * either navigates to trial step or proceeds with full registration.
   */
  public submitForm(): void {
    this.markFormAsDirty(); // Mark all controls as dirty to show validation messages
    console.log('Attempting form submission with form value:', this.form.value); // For debugging

    if (this.form.valid) {
      this.prepareRegisterStudentRequest(); // Always prepare the request data
      if (this.registerService.isCurrentlyRequestingTrial()) {
        this.registerService.navigateToNewTrialStep('free-trial-thank-you');
      } else {
        this.moreDetailsSubmit();
      }
    } else {
      // this.toastService.showError('Please correct the form errors before submitting.');
      console.warn('Form is invalid. Cannot submit.');
    }
  }

  /**
   * Initializes the reactive form group with default values and validators.
   */
  private initializeForm(): void {
    const { addressDto, timeZoneIana, moreDetails } = this.registerService.getRegisterStudentRequest();

    this.form = this.formBuilder.group({
      country: [addressDto?.country || null, Validators.required],
      timeZoneIana: [timeZoneIana || null, Validators.required],
      addressDto: this.formBuilder.group({
        city: [addressDto?.city || null, Validators.required],
        state: [addressDto?.state || null],
        country: [addressDto?.country || null, Validators.required],
      }),
      moreDetails: [moreDetails || null, [CustomValidators.moreDetails]],
    });

    // Dynamically add/remove validators for the 'state' field based on country selection
    this.generalService.setupDynamicStateValidation(this.form, 'country', 'addressDto.state');

    // Set initial selected country if already present in the registration request
    if (addressDto?.country) {
      // Find the country object that matches the name
      // This part might need adjustment if `countries` is not yet loaded here.
      // A safer approach might be to let the child dropdown handle its `formControlName`.
      // For now, assuming `addressDto.country` can be directly set as the selectedCountry if it's a valid ICountry object.
      // If `addressDto.country` is just a string name, you might need to find the full ICountry object.
      if (typeof addressDto.country === 'object' && addressDto.country !== null && 'name' in addressDto.country) {
        this.selectedCountry.set(addressDto.country as ICountry);
      }
    }

    // Set initial selected timezone if already present
    if (timeZoneIana) {
      // Assuming timeZoneIana from registerService is the full ITimezoneData object or has `timezoneValue`
      this.selectedTimezoneSignal.set({ timezoneValue: timeZoneIana, displayName: '' } as ITimezoneData); // Adjust based on actual ITimezoneData structure
    }
  }


  /**
   * Prepares the IRegisterStudentRequest object from the form's current values.
   */
  private prepareRegisterStudentRequest(): void {
    const { city, state, country } = this.form.get('addressDto')?.value || {}; // Destructure nested form group
    const { timeZoneIana, moreDetails } = this.form.value;

    this.registerService.updateRegisterStudentRequest({
      ...this.registerService.getRegisterStudentRequest(),
      timeZoneIana: timeZoneIana?.timezoneValue, // Ensure it's the correct value if `timeZoneIana` is an object
      moreDetails,
      addressDto: {
        country: country?.name, // Ensure country name is used
        city: city,
        state: this.shouldSetAState() ? state : null, // Only include state if required
      }
    });
    console.log('Prepared registration request:', this.registerService.getRegisterStudentRequest());
  }

  /**
   * Executes the API call to register the student.
   * Emits events on success and navigates to the next step.
   */
  private moreDetailsSubmit(): void {
    if (this.form.valid) {
      // Ensure the request is prepared with the latest form data
      this.prepareRegisterStudentRequest();

      const registerStudentRequest = this.registerService.getRegisterStudentRequest();

      // Ensure the timezone value sent to the API is the correct primitive type (e.g., string)
      const formPayload: IRegisterStudentRequest = {
        ...registerStudentRequest,
        timeZoneIana: registerStudentRequest.timeZoneIana as string, // Adjust if ITimezoneData has a different property name
        moreDetails: registerStudentRequest.moreDetails,
        // Ensure other fields are correctly mapped if they were objects from form controls
        addressDto: {
          country: registerStudentRequest.addressDto?.country,
          city: registerStudentRequest.addressDto?.city,
          state: registerStudentRequest.addressDto?.state
        }
      };

      console.log('Final API registration request payload:', formPayload);

      this.apiService.getApiData<IRegisterStudentResponse>(
        { url: IdentityRoutes.postRegisterStudent, method: 'POST' },
        formPayload
      ).pipe(
        this.untilDestroyed() // Use custom helper for unsubscription
      ).subscribe({
        next: (response: IRegisterStudentResponse) => {
          this.eventBusService.emit(new EmitEvent(Events.StudentRegistered, undefined));
          // Assuming getUserClaims() and getUserClaims().role are safe to call after registration success
          const userRole = this.authService.getUserClaims()?.role;
          if (userRole) {
            this.eventBusService.emit(new EmitEvent(Events.StateLoadParentDashboard, {
              role: userRole,
            }));
          }
          this.registerService.navigateToNextStep('register-success', undefined, {studentId: response.studentId, languageOfInterestId: registerStudentRequest.languageOfInterestId,
            teachingLanguageName: registerStudentRequest.languageName
          });
        },
        error: this.handleRegisterStudentError // Centralized error handling
      });
    } else {
      console.warn('Form is invalid, cannot proceed with API submission.');
    }
  }

  /**
   * Sets the selected country signal and updates the registration service request.
   * @param country The selected ICountry object.
   */
  private setSelectedCountry(country: ICountry): void {
    this.selectedCountry.set(country);
    this.registerService.updateRegisterStudentRequest({
      ...this.registerService.getRegisterStudentRequest(),
      addressDto: {
        ...this.registerService.getRegisterStudentRequest().addressDto,
        country: country.name // Store only the country name in the request DTO
      }
    });
  }
}