import { StepDefinition } from "@platform.app/shared/components/dynamic-progress-steps/dynamic-progress-steps.component";



export interface StepDefinitionConfig {
  steps: StepDefinition[];
  baseRoutePattern: string;
  flowTitle: string;
  completionRoute: string;
  disableNavigationOnCompletion: boolean;
}

/**
 * Step definitions for the student registration flow
 */
export const STUDENT_REGISTRATION_STEPS: StepDefinition[] = [
  {
    id: 1,
    route: 'student-name',
    title: 'Student Name',
    shortTitle: 'Name',
    description: 'Enter student\'s full name',
    icon: 'pi pi-user'
  },
  {
    id: 2,
    route: 'student-info',
    title: 'Student Information',
    shortTitle: 'Info',
    description: 'Basic student details and languages',
    icon: 'pi pi-info-circle'
  },
  {
    id: 3,
    route: 'student-availability',
    title: 'Availability',
    shortTitle: 'Schedule',
    description: 'Set learning schedule preferences',
    icon: 'pi pi-calendar'
  },
  {
    id: 4,
    route: 'student-more-details',
    title: 'Additional Details',
    shortTitle: 'Details',
    description: 'Complete student profile information',
    icon: 'pi pi-file-edit'
  },
  {
    id: 5,
    route: 'register-success',
    title: 'Registration Complete',
    shortTitle: 'Complete',
    description: 'Registration successfully completed',
    icon: 'pi pi-check-circle'
  }
];

/**
 * Step definitions for the trial request add new language flow
 */
export const TRIAL_REQUEST_ADD_LANGUAGE_STEPS: StepDefinition[] = [
  {
    id: 1,
    route: 'trial-request-add-new-language',
    title: 'Language Selection',
    shortTitle: 'Language',
    description: 'Choose the language you want to learn',
    icon: 'pi pi-globe'
  },
  {
    id: 2,
    route: 'free-trial-thank-you',
    title: 'New Language Requested Successfully',
    shortTitle: 'Complete',
    description: '',
    icon: 'pi pi-check-circle'
  }
];

/**
 * Configuration for student registration flow
 */
export const STUDENT_REGISTRATION_CONFIG = {
  steps: STUDENT_REGISTRATION_STEPS,
  baseRoutePattern: '/dashboard/parent/request-free-trial',
  flowTitle: 'Student Registration',
  completionRoute: 'register-success',
  disableNavigationOnCompletion: true
};

/**
 * Configuration for trial request add language flow
 */
export const TRIAL_REQUEST_ADD_LANGUAGE_CONFIG = {
  steps: TRIAL_REQUEST_ADD_LANGUAGE_STEPS,
  baseRoutePattern: '/dashboard/parent/request-free-trial/reason',
  flowTitle: 'Add New Language',
  completionRoute: 'free-trial-thank-you',
  disableNavigationOnCompletion: true
};
