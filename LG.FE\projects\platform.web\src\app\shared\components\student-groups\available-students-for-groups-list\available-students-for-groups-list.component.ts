import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, signal, OnInit, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { GeneralService, AuthStateService, HandleApiResponseService, DataApiStateService, EventBusService, IGetStudentsAvailableForGroupingRequest, IStudents, IApiResponseBase, untilDestroyed, IGetStudentsAvailableForGroupingResponse, IStudentsAvailableForGroupingDto, IGetStudentAvailableGroupsRequest, IGetStudentAvailableGroupsResponse, IGetStudentAvailableGroupsDto, GroupDialogState, SkeletonLoaderComponent } from 'SharedModules.Library';
import { StudentGroupSelectionDialogComponent } from '../../dialogs/student-group-selection-dialog/student-group-selection-dialog.component';

@Component({
  selector: 'app-available-students-for-groups-list',
  imports: [
    CommonModule,
    ButtonModule,
    SkeletonLoaderComponent,
  ],
  templateUrl: './available-students-for-groups-list.component.html',
  styleUrl: './available-students-for-groups-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AvailableStudentsForGroupsListComponent implements OnInit {

  @Input() public studentId = '5878ead2-d94d-490a-9b2e-028e3dfd1107';
  @Input() public teachingLanguageId = '1dd2e7c3-60df-4cd3-aa2d-8c673deee91f';
  @Input() public teachingLanguageName = '';
  @Output() studentSelected = new EventEmitter<IStudentsAvailableForGroupingDto>();

  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    api: inject(HandleApiResponseService),
    dataState: inject(DataApiStateService),
    eventBus: inject(EventBusService),
    router: inject(Router)
  };
  private readonly untilDestroyed = untilDestroyed();

  // State signals
  availableStudentsData = signal<IGetStudentsAvailableForGroupingResponse | null>(null);
  availableGroups = signal<IGetStudentAvailableGroupsDto[]>([]);
  isLoading = signal(false);
  hasError = signal(false);
  errorMessage = signal('');

  ngOnInit(): void {
    this.getStudentsAvailableGroups();
    this.getStudentsAvailableForGrouping();
  }

  /**
   * Fetches students available for grouping from the API
   */

  private getStudentsAvailableForGrouping(): void {
    if (!this.studentId || !this.teachingLanguageId) {
      this.hasError.set(true);
      this.errorMessage.set('Student ID and Teaching Language ID are required');
      return;
    }

    this.isLoading.set(true);
    this.hasError.set(false);
    this.errorMessage.set('');

    const params: IGetStudentsAvailableForGroupingRequest = {
      studentId: this.studentId,
      teachingLanguageId: this.teachingLanguageId
    };

    this.services.api.getApiData<IGetStudentsAvailableForGroupingResponse>(
      { url: IStudents.getStudentsAvailableForGrouping, method: 'GET' },
      params
    ).pipe(this.untilDestroyed()).subscribe({
      next: (response: IGetStudentsAvailableForGroupingResponse) => this.handleGetStudentsAvailableForGroupingSuccess(response),
      error: (error: IApiResponseBase) => this.handleGetStudentsAvailableForGroupingError(error),
    });
  }

  private handleGetStudentsAvailableForGroupingSuccess(response: IGetStudentsAvailableForGroupingResponse): void {
    this.isLoading.set(false);
    this.hasError.set(false);
    this.availableStudentsData.set(response);
    console.log('Available students for grouping:', response);
  }

  private handleGetStudentsAvailableForGroupingError(error: IApiResponseBase): void {
    this.isLoading.set(false);
    this.hasError.set(true);
    this.errorMessage.set(error.messages?.[0] || 'Failed to load available students');
    console.error('Error loading available students:', error);
  }

  // UI Interaction Methods
  trackByStudentId(_index: number, student: IStudentsAvailableForGroupingDto): string {
    return student.studentId;
  }

  /**
   * Handles student selection for grouping
   */
  selectStudent(student: IStudentsAvailableForGroupingDto, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    console.log('Student selected for grouping:', student);
    this.studentSelected.emit(student);
  }

  /**
   * Gets the full name of a student
   */
  getStudentFullName(student: IStudentsAvailableForGroupingDto): string {
    return `${student.firstName} ${student.lastName}`.trim();
  }

  /**
   * Gets the full name of the main student
   */
  getMainStudentFullName(): string {
    const data = this.availableStudentsData();
    if (!data) return '';
    return `${data.firstName} ${data.lastName}`.trim();
  }

  /**
   * Refreshes the available students data
   */
  refreshStudents(): void {
    console.log('Refreshing available students...');
    this.getStudentsAvailableForGrouping();
  }

  /**
   * Gets the count of available students
   */
  getAvailableStudentsCount(): number {
    const data = this.availableStudentsData();
    return data?.students?.length || 0;
  }

  /**
   * Gets the display name for the teaching language
   */
  getLanguageDisplayName(): string {
    // For now, return a generic term since the API response structure doesn't include language name
    // This could be enhanced by passing the language name as an input or fetching it separately
    return this.teachingLanguageName || 'the selected language';
  }



  // UI Interaction Methods
  trackByGroupId(_index: number, group: IGetStudentAvailableGroupsDto): string {
    return group.groupId;
  }

  selectGroup(group: IGetStudentAvailableGroupsDto): void {
    console.log('Group selected:', group);
    // Emit event or handle group selection
  }

  joinGroup(group: IGetStudentAvailableGroupsDto, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    console.log('Join group:', group);
    
    this.services.general.openComponent(StudentGroupSelectionDialogComponent, { editMode: true, studentGroupItem: group })
    // Emit event or handle group join
  }

  viewGroupDetails(group: IGetStudentAvailableGroupsDto, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    console.log('View group details:', group);
    this.services.general.openComponent(StudentGroupSelectionDialogComponent, { editMode: false, studentGroupItem: group, groupId: group.groupId, state: GroupDialogState.ViewGroup })
  }


  private getStudentsAvailableGroups(): void {
    const params: IGetStudentAvailableGroupsRequest = {
      studentId: this.studentId,
      parentId: this.services.auth.getUserClaims().id,
      teachingLanguageId: this.teachingLanguageId
    }

    this.services.api.getApiData<IGetStudentAvailableGroupsResponse>(
      { url: IStudents.getStudentAvailableGroups, method: 'GET' },
      params
    ).pipe(this.untilDestroyed()).subscribe({
      next: (response: IGetStudentAvailableGroupsResponse) => this.handleGetStudentsSuccess(response),
      error: (error: IApiResponseBase) => this.handleGetStudentsError(error),
    });
  }

  private handleGetStudentsSuccess(response: IGetStudentAvailableGroupsResponse): void {
    console.log(response);
    this.availableGroups.set(response.availableGroups);
  }

  private handleGetStudentsError(error: IApiResponseBase): void {
    console.error(error);
  }

  navigateToCreateGroup(): void {
    this.services.router.navigate(['/dashboard/parent/groups/create']);
  }
}
