import { IApiResponse, IApiResponseBase, IHttpStatusCode } from 'SharedModules.Library';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, map, catchError, throwError } from 'rxjs';

@Injectable()
export class ApiResponseInterceptor implements HttpInterceptor {
  constructor() { }
  intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<IApiResponse<unknown>>> {
    return next.handle(req).pipe(
      map((event: HttpEvent<unknown>) => {
        if (event instanceof HttpResponse) {
          if (event.body && (event.body as Record<string, unknown>).hasOwnProperty('statusCode')) {
            const apiResponse = event.body as IApiResponse<unknown>;
            if (apiResponse.statusCode !== IHttpStatusCode.OK) {
              throw new Error('Request failed with status code: ' + apiResponse.statusCode);
            }
          }
          return event as HttpResponse<IApiResponse<unknown>>; // Return the data from the response
        }
        return event;
      }),
      catchError((error: IApiResponseBase) => {
        if (error instanceof HttpErrorResponse) {
          // Handle HTTP errors
          console.log(error);
          return throwError(() => error.error as IApiResponseBase); // Use throwError with a factory function
        }
        return throwError(() => error); // Re-throw other errors
      })
    );
  }
}